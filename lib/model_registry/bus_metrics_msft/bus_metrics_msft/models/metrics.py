"""
Data models for bus metrics.
"""
import urllib.parse
from dataclasses import dataclass
from typing import List


@dataclass
class EngineInfo:
    """Represents a user-pool-cluster mapping with completion rate."""

    user: str
    pool: str
    cluster: str
    completion_rate: float


@dataclass
class TopicInfo:
    """Information about a specific topic."""

    topic: str
    engines: List[EngineInfo]

    @property
    def completion_rate(self) -> float:
        """Get total completion rate by summing all engine rates."""
        return sum(engine.completion_rate for engine in self.engines)

    @property
    def grafana_url(self) -> str:
        """Generate Grafana dashboard URL for this topic."""

        base_url = "https://ai-infra-ephgecb0cucpbkg8.wus2.grafana.azure.com"
        dashboard_id = "e972695d-bb0e-4049-8bb0-a07397f14dc4"

        # URL encode the topic
        encoded_topic = urllib.parse.quote(self.topic, safe="")

        # Build the Grafana URL with the specific topic
        grafana_url = f"{base_url}/d/{dashboard_id}/bus-v2-dashboard"

        # Add parameters
        params = [
            "orgId=1",
            "refresh=5m",
            "var-Datasource=aeijxz20txtdsa",
            "var-region=All",
            "var-cluster=All",
            "var-pod_user=All",
            f"var-topic={encoded_topic}",
            "var-brix_pool=All",
            "var-from_user=All",
            "var-bus_line=All",
            "var-qos_type=All",
            "var-brix_pool_regex=.*",
            "var-topic_filter=.*",
            "var-group_by=topic",
            "var-unique_pod=All",
            "var-join_alive_right=group%20by%20%28pool,%20user,%20cluster,%20topic%29%20%28default_oai_bus_alive%7Brole%3D%22.*%22%7D%29",
            f"var-common_filter=topic%3D~%22{encoded_topic}%22,%20pool%3D~%22.*%22,%20pool%3D~%22.*%22,%20topic%3D~%22%5Ebus:.*%22,%20user%3D~%22.*%22",
            "var-_alive_signal_grouped_query_string=%28group%20by%20%28pool,%20user,%20cluster,%20topic%29%20%28default_oai_bus_alive%7Brole%3D",
        ]

        return f"{grafana_url}?{'&'.join(params)}"


@dataclass
class ModelMetrics:
    """Complete metrics for a model."""

    topics: List[TopicInfo]
    snapshot_name: str

    @property
    def has_data(self) -> bool:
        """Check if the metrics contain any data."""
        return len(self.topics) > 0

    @property
    def sorted_topics(self) -> List[TopicInfo]:
        """Get topics sorted by completion rate (highest first)."""
        return sorted(self.topics, key=lambda t: t.completion_rate, reverse=True)
