"""
Prometheus client with Azure WorkloadIdentity authentication.
"""
import logging
import os
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional

import requests
from azure.core.exceptions import AzureError
from azure.identity import DefaultAzureCredential

logger = logging.getLogger(__name__)


class PrometheusClient:
    """Prometheus client with Azure WorkloadIdentity authentication."""

    def __init__(self, prometheus_url: Optional[str] = None):
        """
        Initialize Prometheus client.

        Args:
            prometheus_url: Prometheus URL. If None, will use PROMETHEUS_URL env var.
        """
        self.prometheus_url = prometheus_url or os.getenv("PROMETHEUS_URL")
        if not self.prometheus_url:
            raise ValueError(
                "Prometheus URL must be provided or set in PROMETHEUS_URL environment variable"
            )

        # Remove trailing slash
        self.prometheus_url = self.prometheus_url.rstrip("/")

        # Initialize Azure credential
        self.credential = DefaultAzureCredential()
        self._access_token = None
        self._token_expires_at = None

        logger.info(f"Initialized Prometheus client for URL: {self.prometheus_url}")

    def _get_access_token(self) -> str:
        """Get or refresh Azure access token."""
        now = datetime.utcnow()

        # Check if token is still valid (with 5 minute buffer)
        if (
            self._access_token
            and self._token_expires_at
            and now < self._token_expires_at - timedelta(minutes=5)
        ):
            return self._access_token

        try:
            # Get token for Azure Monitor scope
            token = self.credential.get_token("https://prometheus.monitor.azure.com/.default")
            self._access_token = token.token
            self._token_expires_at = datetime.utcfromtimestamp(token.expires_on)

            logger.debug(f"Acquired new Azure access token, expires at {self._token_expires_at}")
            logger.debug("Successfully refreshed Azure access token")
            return self._access_token

        except AzureError as e:
            logger.error(f"Failed to get Azure access token: {e}")
            raise

    def _make_request(
        self, endpoint: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make authenticated request to Prometheus API.

        Args:
            endpoint: API endpoint (e.g., '/api/v1/query')
            params: Query parameters

        Returns:
            JSON response from Prometheus
        """
        url = f"{self.prometheus_url}{endpoint}"
        headers = {
            "Authorization": f"Bearer {self._get_access_token()}",
            "Content-Type": "application/json",
        }

        try:
            # Log the full request URL for debugging
            logger.debug(f"Making Prometheus request to: {url}")
            if params:
                logger.debug(f"Request parameters: {params}")

            response = requests.get(url, headers=headers, params=params, timeout=30)

            # Log the response status for debugging
            logger.debug(f"Prometheus response status: {response.status_code}")

            response.raise_for_status()

            result = response.json()
            if result.get("status") != "success":
                error_msg = result.get("error", "Unknown error")
                raise ValueError(f"Prometheus query failed: {error_msg}")

            return result

        except requests.RequestException as e:
            # Log more details for 400 errors
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"Request to Prometheus failed: {e}")
                logger.error(f"Response status: {e.response.status_code}")
                logger.error(f"Response headers: {dict(e.response.headers)}")
                try:
                    error_content = e.response.text
                    logger.error(f"Response content: {error_content}")
                except:
                    logger.error("Could not read response content")
            else:
                logger.error(f"Request to Prometheus failed: {e}")
            raise
        except ValueError as e:
            logger.error(f"Invalid response from Prometheus: {e}")
            raise

    def query(self, query: str, time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """
        Execute instant query.

        Args:
            query: PromQL query string
            time: Evaluation timestamp (default: now)

        Returns:
            List of result series from the query
        """
        params = {"query": query}
        if time:
            params["time"] = time.timestamp()

        # Log the query being executed
        logger.debug(f"[PrometheusClient.query] Executing query: {query}")

        try:
            result = self._make_request("/api/v1/query", params)
            series_data = result.get("data", {}).get("result", [])
            logger.debug(
                f"[PrometheusClient.query] Query successful, returned {len(series_data)} series"
            )
            return series_data
        except Exception as e:
            # Try to extract useful error information from the server response
            error_details = self._extract_error_details(e)
            enhanced_error_msg = f"Prometheus query failed: {error_details}"

            logger.error(f"[PrometheusClient.query] {enhanced_error_msg}")
            logger.error(f"[PrometheusClient.query] Failed query was: {query}")

            # Re-raise with enhanced error message
            raise Exception(enhanced_error_msg) from e

    def _extract_error_details(self, exception: Exception) -> str:
        """
        Extract detailed error information from an exception.

        Args:
            exception: The exception to analyze

        Returns:
            Enhanced error message with server response details
        """
        try:
            # Check if it's a requests exception with a response
            if hasattr(exception, "response") and exception.response is not None:
                response = exception.response

                # Try to parse JSON error response
                try:
                    error_data = response.json()
                    if isinstance(error_data, dict):
                        # Extract Prometheus error details
                        if "error" in error_data:
                            server_error = error_data["error"]
                            error_type = error_data.get("errorType", "Unknown")
                            return f"{error_type}: {server_error}"
                        elif "message" in error_data:
                            return error_data["message"]
                except:
                    # If JSON parsing fails, use text content
                    try:
                        content = response.text
                        if content and len(content) < 500:  # Avoid very long responses
                            return f"HTTP {response.status_code}: {content}"
                    except:
                        pass

                # Fallback to basic HTTP error
                return f"HTTP {response.status_code}: {response.reason}"

            # For other types of exceptions, return the original message
            return str(exception)

        except:
            # If all else fails, return the original exception string
            return str(exception)

    def query_range(
        self, query: str, start: datetime, end: datetime, step: str = "15s"
    ) -> Dict[str, Any]:
        """
        Execute range query.

        Args:
            query: PromQL query string
            start: Start timestamp
            end: End timestamp
            step: Query resolution step (e.g., '15s', '1m', '5m')

        Returns:
            Query result
        """
        params = {"query": query, "start": start.timestamp(), "end": end.timestamp(), "step": step}

        logger.info(f"Executing Prometheus range query: {query} from {start} to {end}")
        result = self._make_request("/api/v1/query_range", params)

        return result["data"]

    def get_metrics_list(self) -> List[str]:
        """
        Get list of available metrics.

        Returns:
            List of metric names
        """
        logger.info("Fetching metrics list")
        result = self._make_request("/api/v1/label/__name__/values")

        return result["data"]

    def get_labels(self, metric: Optional[str] = None) -> List[str]:
        """
        Get list of label names.

        Args:
            metric: Optional metric name to filter labels

        Returns:
            List of label names
        """
        endpoint = "/api/v1/labels"
        params = {}

        if metric:
            params["match[]"] = metric

        logger.info(f"Fetching labels for metric: {metric or 'all'}")
        result = self._make_request(endpoint, params)

        return result["data"]

    def get_label_values(self, label: str, metric: Optional[str] = None) -> List[str]:
        """
        Get values for a specific label.

        Args:
            label: Label name
            metric: Optional metric name to filter values

        Returns:
            List of label values
        """
        endpoint = f"/api/v1/label/{label}/values"
        params = {}

        if metric:
            params["match[]"] = metric

        logger.info(f"Fetching values for label '{label}' on metric: {metric or 'all'}")
        result = self._make_request(endpoint, params)

        return result["data"]
