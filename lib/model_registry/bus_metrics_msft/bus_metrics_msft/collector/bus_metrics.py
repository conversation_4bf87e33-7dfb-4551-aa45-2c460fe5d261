"""
Bus metrics collection and processing with dataclass returns.
"""
import logging
from typing import Dict, List, Optional, Tuple

from bus.utils.topics import TopicMode, get_redis_topic
from bus_metrics_msft.models import EngineInfo, ModelMetrics, TopicInfo
from bus_metrics_msft.prometheus import PrometheusClient

logger = logging.getLogger(__name__)


class BusMetricsCollector:
    """Collects and processes bus-related metrics from Prometheus."""

    def __init__(self, prometheus_client: Optional[PrometheusClient] = None):
        """
        Initialize with Prometheus client.

        Args:
            prometheus_client: Prometheus client instance. If None, creates a default instance.
        """
        self.client = prometheus_client or PrometheusClient()

    def _get_topic_pattern(self, snapshot_name: str) -> str:
        """
        Get the Redis topic pattern for a snapshot.

        Args:
            snapshot_name: Model snapshot name

        Returns:
            Topic pattern string
        """
        # Using TopicMode.SHARED generates a topic pattern like "<busline>:snap:<snapshot>"
        # instead of "<busline>:snap:<snapshot>:user:<user>". This shorter pattern works
        # as a regex that matches both shared topics and user-specific topics, allowing
        # us to retrieve data for all users of this snapshot.
        # Note: This assumes the default busline. If multiple buslines are introduced
        # in the future, this approach will need to be revised.
        return get_redis_topic(snapshot_name, topic_mode_or_user=TopicMode.SHARED)

    def get_topic_pool_data(self, snapshot_name: str) -> Dict[str, Dict[str, any]]:
        """
        Get topic and pool data for a specific snapshot.

        Args:
            snapshot_name: Model snapshot name

        Returns:
            Dictionary mapping topic -> {pools: List[str], users: List[str], pool_clusters: Dict[str, str]}

        Raises:
            Exception: If metrics loading fails
        """
        # Build topic pattern
        topic_pattern = self._get_topic_pattern(snapshot_name)

        # Query Prometheus
        query = f"""
        sum(
          default_oai_bus_proxy_requests_received_counter_total{{
            topic=~"{topic_pattern}"
          }}
        ) by (topic, pool, from_user, cluster)
        """

        raw_data = self.client.query(query)

        # Parse and structure the data
        topics = {}
        for item in raw_data:
            if "metric" not in item or "value" not in item:
                continue

            metric = item["metric"]
            topic = metric.get("topic", "")
            pool = metric.get("pool", "")
            user = metric.get("from_user", "")
            cluster = metric.get("cluster", "")

            if not topic:
                continue

            if topic not in topics:
                topics[topic] = {"pools": set(), "users": set(), "pool_clusters": {}}

            if pool:
                topics[topic]["pools"].add(pool)
                # Store cluster information for each pool
                if cluster:
                    topics[topic]["pool_clusters"][pool] = cluster
            if user:
                topics[topic]["users"].add(user)

        # Convert sets to lists for JSON serialization
        structured_topics = {}
        for topic, data in topics.items():
            structured_topics[topic] = {
                "pools": sorted(list(data["pools"])),
                "users": sorted(list(data["users"])),
                "pool_clusters": data["pool_clusters"],  # Keep cluster mapping
            }

        return structured_topics

    def get_completion_rates_for_topics(
        self, snapshot_name: str
    ) -> Dict[str, Dict[Tuple[str, str, str], float]]:
        """
        Get completion rates for all topics related to a snapshot.

        Args:
            snapshot_name: Model snapshot name

        Returns:
            Dictionary mapping topic -> {(user, pool, cluster): completion_rate}

        Raises:
            Exception: If metrics loading fails
        """
        # Build topic pattern
        topic_pattern = self._get_topic_pattern(snapshot_name)

        # Query completion rates - include cluster information
        query = f"""
        sum by (topic, from_user, pool, cluster) (
          rate(
            default_oai_bus_requestor_finished_counter_total{{
              topic=~"{topic_pattern}"
            }}[5m]
          )
        )
        """

        raw_data = self.client.query(query)

        # Parse completion rate data
        completion_rates = {}
        for item in raw_data:
            if "metric" not in item or "value" not in item:
                continue

            metric = item["metric"]
            topic = metric.get("topic", "")
            user = metric.get("from_user", "")
            pool = metric.get("pool", "")
            cluster = metric.get("cluster", "")

            try:
                rate = float(item["value"][1])
            except (ValueError, IndexError):
                continue

            if topic and user and pool:
                if topic not in completion_rates:
                    completion_rates[topic] = {}
                # Use (user, pool, cluster) as key to include cluster info
                completion_rates[topic][(user, pool, cluster)] = rate

        return completion_rates

    def load_model_metrics(self, snapshot_name: str) -> ModelMetrics:
        """
        Load complete model metrics for a snapshot (merged functionality).

        Args:
            snapshot_name: Model snapshot name

        Returns:
            Structured model metrics

        Raises:
            Exception: If metrics loading fails
        """
        # Get raw data from Prometheus using the new methods
        topic_data = self.get_topic_pool_data(snapshot_name)
        completion_rates = self.get_completion_rates_for_topics(snapshot_name)

        # Transform raw data into structured objects
        topics = self._build_topic_info_list(topic_data, completion_rates)

        return ModelMetrics(topics=topics, snapshot_name=snapshot_name)

    def _build_topic_info_list(
        self,
        topic_data: Dict[str, Dict[str, any]],
        completion_rates: Dict[str, Dict[Tuple[str, str, str], float]],
    ) -> List[TopicInfo]:
        """Build list of TopicInfo objects from raw data."""
        topics = []

        for topic, topic_info in topic_data.items():
            if not isinstance(topic_info, dict) or "pools" not in topic_info:
                continue

            # Get completion rates for this topic
            topic_rates = completion_rates.get(topic, {})

            # Build engine info for ALL pools (active and inactive)
            engines = []

            # First, add all active pools with their rates and cluster info
            active_pools = set()
            for (user, pool, cluster), rate in topic_rates.items():
                engines.append(
                    EngineInfo(user=user, pool=pool, cluster=cluster, completion_rate=rate)
                )
                active_pools.add(pool)

            # Then, add inactive pools (those in topic_info["pools"] but not in topic_rates)
            all_pools = set(topic_info["pools"])
            for pool in all_pools - active_pools:
                # Get cluster info from pool_clusters mapping if available
                cluster = topic_info.get("pool_clusters", {}).get(pool, "Unknown")
                # Find the first user for this pool from the users list
                if topic_info["users"]:
                    engines.append(
                        EngineInfo(
                            user=topic_info["users"][0],
                            pool=pool,
                            cluster=cluster,
                            completion_rate=0.0,
                        )
                    )

            # Sort by completion rate descending (active pools first)
            engines.sort(key=lambda x: x.completion_rate, reverse=True)

            topics.append(
                TopicInfo(
                    topic=topic,
                    engines=engines,
                )
            )

        return topics
