# Bus Metrics MSFT

A unified library for collecting and processing Prometheus bus metrics, designed as a metrics inspector for applications to reuse.

## Overview

This library consolidates the functionality previously split between:
- `model_registry_discover_msft.metrics.bus_metrics` (BusMetricsCollector)
- `model_registry_discover_msft.prometheus.client` (PrometheusClient)  
- `busman_msft.models.metrics` (data classes)

## Features

- **Unified API**: Single library providing all bus metrics functionality
- **Type-safe Data Classes**: Strong typing with dataclasses for all return values
- **Azure Integration**: Built-in Azure WorkloadIdentity authentication for Prometheus
- **Exception Transparency**: Methods raise exceptions instead of returning inconsistent error structures
- **Optional Dependencies**: PrometheusClient parameter is optional - uses default instance if not provided
- **Reusable**: Designed for use across multiple applications

## Usage

### Simple Usage (Recommended)
```python
from bus_metrics_msft.collector.bus_metrics import BusMetricsCollector

# Initialize with default PrometheusClient
collector = BusMetricsCollector()

# Get complete metrics in one call
model_metrics = collector.load_model_metrics("my-model-snapshot")

# Access structured data
for topic_info in model_metrics.sorted_topics:
    print(f"Topic: {topic_info.topic}")
    print(f"Completion Rate: {topic_info.completion_rate:.2f} req/s")
    print(f"Grafana URL: {topic_info.grafana_url}")
    
    for engine in topic_info.engines:
        print(f"  Engine: {engine.user}@{engine.pool} ({engine.cluster})")
```

### Advanced Usage (Custom PrometheusClient)
```python
from bus_metrics_msft.collector.bus_metrics import BusMetricsCollector
from bus_metrics_msft.prometheus.client import PrometheusClient

# Initialize with custom client
custom_client = PrometheusClient("https://your-prometheus-url")
collector = BusMetricsCollector(custom_client)

# Get structured data
model_metrics = collector.load_model_metrics("my-model-snapshot")
```

## Data Classes

- `ModelMetrics`: Complete metrics for a model snapshot with sorting and data validation
- `TopicInfo`: Information about a specific topic with computed completion rate and Grafana URL generation  
- `EngineInfo`: User-pool-cluster mapping with completion rates

## Key Improvements from Previous Libraries

### Simplified Constructor
```python
# Before - required PrometheusClient
from bus_metrics_msft import BusMetricsCollector, PrometheusClient
client = PrometheusClient()
collector = BusMetricsCollector(client)

# After - PrometheusClient is optional
from bus_metrics_msft.collector.bus_metrics import BusMetricsCollector
collector = BusMetricsCollector()  # Uses default PrometheusClient
```

### Computed Properties
```python
# Before - separate fields that could get out of sync
topic_info.completion_rate = 15.5  # Could be wrong
topic_info.pools = ["pool1", "pool2"]  # Could be outdated

# After - computed from engines list
topic_info.completion_rate  # Always accurate sum of engine rates
# pools/users properties removed to eliminate redundancy
```

### Explicit Imports
```python
# Before - complex __init__.py with try/except patterns
from bus_metrics_msft import BusMetricsCollector  # Could fail silently

# After - explicit imports
from bus_metrics_msft.collector.bus_metrics import BusMetricsCollector
from bus_metrics_msft.models.metrics import TopicInfo, EngineInfo, ModelMetrics
from bus_metrics_msft.prometheus.client import PrometheusClient
```

### Error Handling Changes
```python
# Before - methods returned dicts with "error" keys
result = collector.get_topic_pool_data("model")
if "error" in result:
    handle_error(result["error"])

# After - methods raise exceptions directly
try:
    result = collector.get_topic_pool_data("model")
except Exception as e:
    handle_error(str(e))
```

## Dependencies

- `requests`: HTTP client for Prometheus API
- `azure-identity`: Azure authentication
- `azure-core`: Azure core functionality
- `bus` (monorepo): Bus utilities for topic pattern generation
