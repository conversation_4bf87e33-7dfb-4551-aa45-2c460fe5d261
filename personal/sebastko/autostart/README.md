# Peashooter autostart

The feature allows to schedule a peashooter cluster with an auto-start script
which will be executed once the cluster is up and running. The cluster will
be automatically deleted after the job is finished. If there are failures
in the job, by default it will be retried 3 times (can be changed with the `autostart_max_attempts` argument).

It also introduces `pychz` which has become popular recently within OAI to
prepare and submit jobs (even without auto-start).

## Pre-requisites
As of 8/5/2025, unfortunately you need to use a private branch of `torchflow-mirror` to support autostart:

 * May version of the code: `sebastko/may_fi_autostart_clean` (which includes changes cherry-picked from OAI side, as well as some improvements/fixes on the MSFT side which I'm planning to upstream)
 * July (unofficial) version of the code: `sebastko/0710-rebase-autostart-clean` (July already had the needed OAI-side changes, but the branch has a bunch of MSFT-side improvements/fixes)

Also make sure `pychz` is installed on your laptop:
```
oaipkg install pychz
```

## Usage
Minimal example from the laptop:
```
pychz qstart_autostart_example.chz.py -e launch_cluster \
    cluster=<your-cluster>
```
You can also override more arguments, e.g.:
```
pychz qstart_autostart_example.chz.py -e launch_cluster \
    team=<your-team> cluster=<your-cluster> priority=<your-priority> prefix=myexp
```

The training/eval is run within a `tmux` background session, so if you'd like to view logs in the real-time (before the cluster is self-deleted), you can SSH into the pod and run:
```
tmux attach -t 0
```

## autostart + brix queues
I had high hopes for using this feature together with brix queues (see: `b --help | grep queue`), but I couldn't get it to work, i.e., the peashooters don't seem get scheduled correctly. Needs more investigation.
