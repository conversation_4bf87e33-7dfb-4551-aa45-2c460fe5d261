"""
Schedules peashooter, automatically starts Q* training on MATH and automatically
shuts down the cluster after 1 step of training.

Usage from the laptop:
```
pychz qstart_autostart_example.chz.py -e launch_cluster \
    cluster=<your-cluster>
```
See README.md for more details.
"""

import datetime
import os

# TODO: expose in a separate library.
CLUSTER_TO_STORAGE_MAP = {
    "prod-uksouth-7": "orngcresco",
    "prod-uksouth-8": "orngcresco",
    "prod-southcentralus-hpe-2": "orngscuscresco",
    "prod-southcentralus-hpe-3": "orngscuscresco",
    "prod-southcentralus-hpe-5": "orngscuscresco",
}


def launch_cluster(
    cluster,
    priority="low-priority",
    team=None,
    queue=None,
    experiment_name=None,
    prefix="auto",
    base_storage=None
):
    if experiment_name is None:
        experiment_name = f"{prefix}-{datetime.datetime.now():%Y%m%d%H%M%S}"
    if base_storage is None:
        base_storage = CLUSTER_TO_STORAGE_MAP[cluster]

    lines = ["python -m qstar.peashooter.run_peashooter"]
    if team:
        lines.append(f"rapid.cluster.team={team}")
    if queue:
        lines.append(f"rapid.cluster.queue={queue}")
    lines += [
        f"rapid.cluster.name={cluster}",
        f"rapid.cluster.priority={priority}",
        "n_sampling_gpus=8",
        "n_train_gpus=16",
        "cpu_controller=True",
        "n_cpu_cores_for_controller=4",
        f"autostart_file={__file__}",
        f'autostart_args="experiment_name={experiment_name} base_storage={base_storage}"',
        "autostart_max_attempts=3",
        "",
        "# Local validation requires qstar dependencies…",
        "autostart_skip_validation=True",
        "security_profile=msft-orng",
        "",
        f"rapid.rapid_id={experiment_name}",
    ]

    return "\n".join(lines) + "\n"


def run_experiment(experiment_name, base_storage="orngcresco"):
    return f"""
python3 -m qstar.run_experiment
name="{experiment_name}"
:berry_models.scallion:d16_80g
policy.initial_checkpoint=az://{base_storage}/models/snapshots/models.tc.small/scallion-qstar-prior-d16-transferred-20241118/
:qstar.presets.mathgen:tr_mathd_v2
batcher.curriculum.training_datasets.0.dataset.dataset_container={base_storage} 
batcher.curriculum.training_datasets.0.dataset.dataset_id=data.mathd-msft.train 
defaults.n_ctx=4096

enable_slackbot=False 
security_profile=msft-orng 
github_upload=False 
wandb_enable=True 
kafka_enable=False 

max_steps=1
"""


_CMD = run_experiment
