import json
import logging
from datetime import datetime
from functools import partial
from typing import Any, Literal, cast

import chz
import structlog
import tenacity
from berry.function_wrapper import FunctionWrapper
from berry_harmony.tools.berry_tool_interface import ResourceConfig, ToolConfig
from caas_tool.caas_container import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from chat.render.v4.experimental.strawberry.formatter import BerryChannel
from deep_swe.datasets.configs import (
    DeepSWEDatasetConfig,
    DeepSWEVardiscProducer,
    conversation_converter,
    make_multistage_grader,
)
from deep_swe_msft.env_setup_padawan_v2.utils.train_constants import ENABLE_NETWORK
from deep_swe_msft.padawan_data.system_prompt import (
    INSTRUCTION_PADAWAN,
    INSTRUCTION_PADAWAN_HARD,
    PADAWAN_MODEL_IDENTITY,
    PADAWAN_SYSTEM_PROMPT,
)
from deep_swe_msft.padawan_graders.cotograder_utils import (
    COTOGRADER_BUS_TOPIC,
    COTOGRADER_BUS_USER,
    COTOGRADER_REDIS_ID,
    COTOGRADER_RENDERER,
)
from deep_swe_msft.swe_bench_train_v2_padawan_v2.data_customization.conversation_init import (
    conversation_init_fn as padawan_conversation_init_fn,
)
from deep_swe_msft.swe_bench_train_v2_padawan_v2.dataset_config import (
    enforce_commands as enforce_padawan_commands,
)
from deep_swe_msft.swe_bench_train_v2_padawan_v2.dataset_config import (
    get_padawan_system_prompt,
    random_tool_set,
)
from deep_swe_msft.swe_bench_train_v2_vsc.dataset_config import CotoGraderService
from deep_swe_msft.swe_bench_train_v2_vsc.dataset_config import (
    enforce_commands as enforce_vsc_commands,
)
from deep_swe_msft.tools.caas_mix_tool import MixToolConfig
from deep_swe_msft.tools.caas_padawan_tool import PadawanToolConfig
from deep_swe_msft.tools.caas_vscode_tool import VSCodeToolConfig
from deep_swe_msft.tools.utils import CAAS_ENDPOINT, CAAS_IMAGE, MIX_VSC_TOOL, PDW_MNT
from deep_swe_msft.tools.vscode_copilot_tool import VSC_MNT, setup_vscutils
from prbot_msft.configs.caas_container_tool import (
    SweBenchHardCaasContainerResourceConfig,
    SWEBenchHardContainerToolConfig,
    SweBenchHardSetupFn,
    SweBenchHardVSCCaasContainerResourceConfig,
)
from prbot_msft.configs.utils import include_install_for_easy_langs, log_with_timestamp
from prbot_msft.data_customization.noexec_conversation_init import INSTR_PREAMBLE as NOEXEC_PREAMBLE
from prbot_msft.data_customization.noexec_conversation_init import (
    INSTR_PREAMBLE as NON_EXEC_PREAMBLE,
)
from prbot_msft.data_customization.noexec_conversation_init import REPAIR_SUFFIX as NOEXEC_SUFFIX
from prbot_msft.data_customization.noexec_test_conversation_init import TEST_PREAMBLE, TEST_SUFFIX
from prbot_msft.data_customization.noexec_test_conversation_init import (
    conversation_init_fn as noexec_test_conversation_init_fn,
)
from prbot_msft.data_customization.noexec_test_conversation_init import (
    get_problem as noexec_test_get_problem,
)
from prbot_msft.data_customization.repro_conversation_init import REPRO_TASK_GOAL
from prbot_msft.data_customization.swebenchhard_prompts import INSTR_END as PADAWAN_INSTR_END
from prbot_msft.data_customization.swebenchhard_prompts import (
    INSTR_PREAMBLE as PADAWAN_INSTR_PREAMBLE,
)
from prbot_msft.graders.swebenchhard_double_rkld_grader import DoubleRkldGrader
from prbot_msft.graders.swebenchhard_repair_comparison_grader import (
    SWEBenchHardRepairComparisonGrader,
)
from prbot_msft.graders.swebenchhard_repair_criteria_grader import SWEBenchHardRepairCriteriaGrader
from prbot_msft.graders.swebenchhard_repro_criteria_grader import SWEBenchHardReproCriteriaGrader
from prbot_msft.graders.swebenchhard_test_comparison_grader import SWEBenchHardTestComparisonGrader
from prbot_msft.graders.swebenchhard_test_criteria_grader import SWEBenchHardTestCriteriaGrader
from prbot_msft.tools.coreutils import setup_fn_coreutils
from prbot_msft.tools.get_padawan_tools_v2 import setup_fn_padawan_v2
from qstar.common import datapoint
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from qstar.curriculums.function_wrapper import FunctionWrapper
from qstar.curriculums.variant_producer import VariantProducer
from qstar.graders.grader import Grader
from qstar.graders.multi_stage_grader import MultiStageGrader
from qstar.graders.taskgen_utils import TokenCompleterGraderService
from qstar.presets.chz_utils import IsOverride, override

logger = structlog.get_logger(component=__name__)

O3_TOPIC_MODE_OR_USER = COTOGRADER_BUS_USER
O3_TOPIC_OR_SNAPSHOT = COTOGRADER_BUS_TOPIC
O3_BUS_RENDERER = COTOGRADER_RENDERER
O3_BUS_REDIS_ID = COTOGRADER_REDIS_ID


def replace_problem_with_user_prompt(
    dp: dict[str, Any],
) -> list[dict[str, Any]]:
    if dp.get("metadata", {}).get("user_prompt"):
        dp["problem"] = dp["metadata"]["user_prompt"][-1]["content"]["parts"][0]

    return [dp]


def replace_problem_with_user_prompt_wrapper() -> FunctionWrapper:
    return FunctionWrapper(
        name="prbot_msft.configs.swebench_hard:replace_problem_with_user_prompt",
    )


def padawan_conversation_init_fn_wrapper(**kwargs):
    return padawan_conversation_init_fn(
        **kwargs,
        instruction=INSTRUCTION_PADAWAN,
        add_contributor_criteria=False,
        get_problem=lambda dp: dp["metadata"]["problem_statement"],
        get_repo=lambda dp: dp["metadata"]["repo"],
        get_issue_numbers=lambda dp: [dp["metadata"]["instance_id"].split("-")[-1]],
        custom_instruction_preamble=PADAWAN_INSTR_PREAMBLE,
        extra_appended_instructions=(PADAWAN_INSTR_END,),
    )


def padawan_noexec_conversation_init_fn_wrapper(**kwargs):
    return padawan_conversation_init_fn(
        **kwargs,
        instruction=INSTRUCTION_PADAWAN
        if include_install_for_easy_langs(kwargs["datapoint"])
        else INSTRUCTION_PADAWAN_HARD,
        add_contributor_criteria=False,
        get_problem=lambda dp: dp["metadata"]["problem_statement"],
        get_repo=lambda dp: dp["metadata"]["repo"],
        get_issue_numbers=lambda dp: [dp["metadata"]["instance_id"].split("-")[-1]],
        custom_instruction_preamble=NOEXEC_PREAMBLE,
        extra_appended_instructions=(NOEXEC_SUFFIX,),
        include_install_instructions=include_install_for_easy_langs,
    )


def padawan_noexec_test_conversation_init_fn_wrapper(**kwargs):
    return padawan_conversation_init_fn(
        **kwargs,
        add_contributor_criteria=False,
        get_problem=lambda dp: noexec_test_get_problem(datapoint=dp),
        get_repo=lambda dp: dp["metadata"]["repo"],
        get_issue_numbers=lambda dp: [dp["metadata"]["instance_id"].split("-")[-1]],
        custom_instruction_preamble=TEST_PREAMBLE,
        extra_appended_instructions=(TEST_SUFFIX,),
    )


def get_sbh_criteria_grader_chz_argv(
    topic_mode_or_user: str = "msft-swebenchhard",
    topic_or_snapshot: str = "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    bus_renderer: str = "harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
    bus_redis_id: str = "msft-swebenchhard-1",
):
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV topic_or_snapshot: {topic_or_snapshot}")
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV topic_mode_or_user: {topic_mode_or_user}")
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV bus_renderer: {bus_renderer}")

    SBHCRITERIAGRADER_CHZ_ARGV = [
        f"grader_service.token_completer.bus_line=bus",
        "grader_service.token_completer.qos_type=bus_token_completer:QoSType.ROUND_ROBIN_BY_POD",
        f"grader_service.token_completer.topic_mode_or_user={topic_mode_or_user}",
        f"grader_service.token_completer.topic_or_snapshot={topic_or_snapshot}",
        "grader_service.token_completer=bus_token_completer:BusTokenCompleter.Config",
        f"grader_service.redis_config_id=msft-swebenchhard-1",
        "grader_service=TokenCompleterGraderService",
        f"renderer_name={bus_renderer}",
    ]
    log_with_timestamp(f"SBHCRITERIAGRADER_CHZ_ARGV: {SBHCRITERIAGRADER_CHZ_ARGV}")
    return SBHCRITERIAGRADER_CHZ_ARGV


def _make_padawan_tool_configs(
    container_tool_config: PadawanToolConfig,
) -> tuple[ToolConfig, ...]:
    return (container_tool_config,)


@chz.chz(typecheck=True)
class SWEBenchReproCriteriaCotGrader(SWEBenchHardReproCriteriaGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    grader_max_tokens: int = 16384
    renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"


@chz.chz(typecheck=True)
class SWEBenchRepairCriteriaCotGrader(SWEBenchHardRepairCriteriaGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    grader_max_tokens: int = 16384
    renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"


@chz.chz(typecheck=True)
class SWEBenchRepairComparisonCotGrader(SWEBenchHardRepairComparisonGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    grader_max_tokens: int = 16384
    renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"


@chz.chz(typecheck=True)
class SWEBenchTestCriteriaCotGrader(SWEBenchHardTestCriteriaGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    grader_max_tokens: int = 16384
    renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"


@chz.chz(typecheck=True)
class SWEBenchTestComparisonCotGrader(SWEBenchHardTestComparisonGrader, IsOverride):
    grader_service: TokenCompleterGraderService = override(CotoGraderService)
    grader_max_tokens: int = 16384
    renderer_name: str = "harmony_v4.0.15_berry_v3_1mil_orion_lpe"


def make_swebenchhard_multistage_grader(
    # arguments below can be overridden via chz wildcards
    scenario: Literal["repair", "repro", "rkld"] = "repair",
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    enable_test_grader: bool = False,
    enable_thoroughness_grader: bool = False,
    enable_vsc_func_enforcement_grader: bool = False,
    use_lupo_and_likertpp_graders: bool = False,
    use_padawan_enforcement_grader: bool = False,
    topic_mode_or_user: str = "msft-swebenchhard",
    rkld_topic_mode_or_user: str = "msft-swebenchhard",
    topic_or_snapshot: str = "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    bus_renderer: str = "harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
    bus_redis_id: str = "msft-swebenchhard-1",
    rkld_snapshot: str = "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
) -> MultiStageGrader | DoubleRkldGrader:
    if scenario == "rkld":
        return DoubleRkldGrader(
            topic=rkld_snapshot,
            topic_mode_or_user=rkld_topic_mode_or_user,
            line="bus",
        )

    # Commented out since this is not tested yet!
    # elif scenario == "rkld-multi":
    #     grader_argvs = [
    #         [
    #             "=prbot_msft.graders.swebenchhard_double_rkld_grader:DoubleRkldGrader",
    #             f"topic={rkld_snapshot}",
    #             f"topic_mode_or_user={topic_mode_or_user}",
    #             "line=bus",
    #         ],
    #     ]

    elif scenario == "repro":
        grader_argvs = [
            ["=prbot_msft.graders.swebenchhard_repro_grader:SWEBenchHardReproGrader"],
            [
                "=prbot_msft.configs.swebench_hard:SWEBenchReproCriteriaCotGrader",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        ]
    else:
        grader_argvs = [
            ["=prbot_msft.graders.swebenchhard_repair_grader:SWEBenchHardRepairGrader"],
            [
                "=prbot_msft.configs.swebench_hard:SWEBenchRepairCriteriaCotGrader",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        ]
    if use_padawan_enforcement_grader:
        grader_argvs.append(
            [
                "=deep_swe_msft.padawan_graders.system_prompt_following_grader:SPFollowCotograder",
                f"grader_max_tokens={16384}",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        )

    if enable_vsc_func_enforcement_grader:
        grader_argvs.append(
            [
                "=deep_swe_msft.swe_bench_train_v2_vsc.graders.func_enforcement_grader:FuncEnforcementGrader",
                "target_ratio=0.1",
            ]
        )

    return make_multistage_grader(grader_argvs, channels_for_answer, use_lupo_and_likertpp_graders)


def make_swebenchhard_multistage_or_grader(
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_lupo_and_likertpp_graders: bool = False,
    topic_mode_or_user: str = "msft-swebenchhard",
    topic_or_snapshot: str = "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    bus_renderer: str = "harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
    bus_redis_id: str = "msft-swebenchhard-1",
    short_circuit: bool = True,
) -> MultiStageGrader:
    grader_argvs = [
        [
            "=prbot_msft.graders.swebenchhard_repair_combined_grader:SWEBenchHardRepairCombinedGrader",
            "comparison_grader=prbot_msft.configs.swebench_hard:SWEBenchRepairComparisonCotGrader",
            *(
                f"comparison_grader.{item}"
                for item in get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                )
            ),
            "execution_grader=prbot_msft.graders.swebenchhard_repair_grader:SWEBenchHardRepairGrader",
            f"short_circuit={short_circuit}",
        ],
        [
            "=prbot_msft.configs.swebench_hard:SWEBenchRepairCriteriaCotGrader",
            *get_sbh_criteria_grader_chz_argv(
                topic_mode_or_user=topic_mode_or_user,
                topic_or_snapshot=topic_or_snapshot,
                bus_renderer=bus_renderer,
                bus_redis_id=bus_redis_id,
            ),
            "use_final_patch=True",
        ],
    ]

    return make_multistage_grader(grader_argvs, channels_for_answer, use_lupo_and_likertpp_graders)


def make_swebenchhard_multistage_silver_grader(
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_lupo_and_likertpp_graders: bool = False,
    use_padawan_enforcement_grader: bool = False,
    topic_mode_or_user: str = "msft-swebenchhard",
    topic_or_snapshot: str = "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    bus_renderer: str = "harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
    bus_redis_id: str = "msft-swebenchhard-1",
) -> MultiStageGrader:
    grader_argvs = []
    if use_padawan_enforcement_grader:
        grader_argvs.append(
            [
                "=deep_swe_msft.padawan_graders.system_prompt_following_grader:SPFollowCotograder",
                f"grader_max_tokens={16384}",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        )
    grader_argvs.append(
        [
            "=prbot_msft.configs.swebench_hard:SWEBenchRepairComparisonCotGrader",
            *get_sbh_criteria_grader_chz_argv(
                topic_mode_or_user=topic_mode_or_user,
                topic_or_snapshot=topic_or_snapshot,
                bus_renderer=bus_renderer,
                bus_redis_id=bus_redis_id,
            ),
        ],
    )

    return make_multistage_grader(grader_argvs, channels_for_answer, use_lupo_and_likertpp_graders)


def make_swebenchhard_test_multistage_grader(
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_lupo_and_likertpp_graders: bool = False,
    topic_mode_or_user: str = "msft-swebenchhard",
    topic_or_snapshot: str = "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    bus_renderer: str = "harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
    bus_redis_id: str = "msft-swebenchhard-1",
    is_execution_grader: bool = False,
    use_padawan_enforcement_grader: bool = False,
) -> MultiStageGrader:
    if is_execution_grader:
        grader_argvs = [
            ["=prbot_msft.graders.swebenchhard_test_grader:SWEBenchHardTestGrader"],
            [
                "=prbot_msft.configs.swebench_hard:SWEBenchTestCriteriaCotGrader",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        ]
    else:
        grader_argvs = [
            [
                "=prbot_msft.configs.swebench_hard:SWEBenchTestComparisonCotGrader",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
            [
                "=prbot_msft.configs.swebench_hard:SWEBenchTestCriteriaCotGrader",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        ]

    if use_padawan_enforcement_grader:
        grader_argvs.append(
            [
                "=deep_swe_msft.padawan_graders.system_prompt_following_grader:SPFollowCotograder",
                f"grader_max_tokens={16384}",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        )

    return make_multistage_grader(grader_argvs, channels_for_answer, use_lupo_and_likertpp_graders)


def make_swebenchhard_test_multistage_or_grader(
    # arguments below can be overridden via chz wildcards
    channels_for_answer: tuple[BerryChannel | None, ...] = (BerryChannel.FINAL_ANSWER,),
    use_lupo_and_likertpp_graders: bool = False,
    topic_mode_or_user: str = "msft-swebenchhard",
    topic_or_snapshot: str = "az://orngcresco/models/snapshots/sweberry-v2-nv4-final-20250207-decrypted",
    bus_renderer: str = "harmony_v4.0.15_berry_v3_256k_orion_lpe_wtruncation",
    bus_redis_id: str = "msft-swebenchhard-1",
    short_circuit: bool = True,
    use_padawan_enforcement_grader: bool = False,
) -> MultiStageGrader:
    grader_argvs = [
        [
            "=prbot_msft.graders.swebenchhard_repair_combined_grader:SWEBenchHardRepairCombinedGrader",
            "comparison_grader=prbot_msft.configs.swebench_hard:SWEBenchTestComparisonCotGrader",
            *(
                f"comparison_grader.{item}"
                for item in get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                )
            ),
            "execution_grader=prbot_msft.graders.swebenchhard_test_grader:SWEBenchHardTestGrader",
            f"short_circuit={short_circuit}",
        ],
        [
            "=prbot_msft.configs.swebench_hard:SWEBenchTestCriteriaCotGrader",
            *get_sbh_criteria_grader_chz_argv(
                topic_mode_or_user=topic_mode_or_user,
                topic_or_snapshot=topic_or_snapshot,
                bus_renderer=bus_renderer,
                bus_redis_id=bus_redis_id,
            ),
        ],
    ]
    if use_padawan_enforcement_grader:
        grader_argvs.append(
            [
                "=deep_swe_msft.padawan_graders.system_prompt_following_grader:SPFollowCotograder",
                f"grader_max_tokens={16384}",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        )

    if use_padawan_enforcement_grader:
        grader_argvs.append(
            [
                "=deep_swe_msft.padawan_graders.system_prompt_following_grader:SPFollowCotograder",
                f"grader_max_tokens={16384}",
                *get_sbh_criteria_grader_chz_argv(
                    topic_mode_or_user=topic_mode_or_user,
                    topic_or_snapshot=topic_or_snapshot,
                    bus_renderer=bus_renderer,
                    bus_redis_id=bus_redis_id,
                ),
            ],
        )

    return make_multistage_grader(grader_argvs, channels_for_answer, use_lupo_and_likertpp_graders)


def replace_problem_with_user_prompt(
    dp: dict[str, Any],
) -> list[dict[str, Any]]:
    user_prompt = dp.get("metadata", {}).get("user_prompt", None)
    instance_id = dp.get("metadata", {}).get("instance_id", "unknown")
    if user_prompt:
        log_with_timestamp(
            f"[{instance_id}] Replacing problem with user prompt: \nPROBLEM: {dp['problem']}\nUSER_PROMPT: {user_prompt}\n"
        )
        dp["problem"] = dp["metadata"]["user_prompt"][-1]["content"]["parts"][0]
    else:
        log_with_timestamp(
            f"[{instance_id}] Not replacing problem with user prompt: \nPROBLEM: {dp['problem']}\nUSER_PROMPT: {user_prompt}\n"
        )

    return [dp]


def replace_problem_with_user_prompt_wrapper() -> FunctionWrapper:
    return FunctionWrapper(
        name="prbot_msft.configs.swebench_hard:replace_problem_with_user_prompt",
    )


@chz.chz
class SWEBenchHardRepairTrainDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = (SWEBenchHardContainerToolConfig(),)
    dataset_container: str = chz.field(default="orngcresco")
    dataset_id: str = "data.damajercak.swe.upload05202025.rcr_12878.train_hq"
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_swebenchhard_multistage_grader, scenario="repair")
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_fn_coreutils)
            ),
        )
    )
    max_num_yields: int = 256
    variant_producer: VariantProducer | None = override(DeepSWEVardiscProducer)


def _make_vsc_tool_configs(
    container_tool_config: VSCodeToolConfig,
) -> tuple[ToolConfig, ...]:
    return (container_tool_config,)


@chz.chz
class SWEBenchHardRepairTrainWithOrDatasetConfig(SWEBenchHardRepairTrainDatasetConfig, IsOverride):
    grader: Grader[HarmonyCompletionDatapoint] = override(make_swebenchhard_multistage_or_grader)


@chz.chz
class SWEBenchHardRepairTrainWithSilverDatasetConfig(
    SWEBenchHardRepairTrainDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        make_swebenchhard_multistage_silver_grader
    )


@chz.chz
class SWEBenchHardTestTrainWithSilverDatasetConfig(
    SWEBenchHardRepairTrainDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_swebenchhard_test_multistage_grader, is_execution_grader=False)
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_fn_coreutils), apply_code_patch=True
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                func_name="prbot_msft.data_customization.noexec_test_conversation_init:conversation_init_fn",
            ),
            replace_problem_with_user_prompt_wrapper(),
        )
    )


@chz.chz
class SWEBenchHardTestTrainExecDatasetConfig(
    SWEBenchHardTestTrainWithSilverDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_swebenchhard_test_multistage_grader, is_execution_grader=True)
    )


@chz.chz
class SWEBenchHardRepairRKLDTrainDatasetConfig(SWEBenchHardRepairTrainDatasetConfig, IsOverride):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_swebenchhard_multistage_grader, scenario="rkld")
    )


@chz.chz
class SWEBenchHardRepairTrainNoExecWithSilverDatasetConfig(
    SWEBenchHardRepairTrainDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        make_swebenchhard_multistage_silver_grader
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "prbot_msft.data_customization.noexec_conversation_init:conversation_init_fn"
            ),
            replace_problem_with_user_prompt_wrapper(),
        )
    )


@chz.chz
class SWEBenchHardRepairRKLDTrainDatasetConfig(HarmonyCompletionDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = (SWEBenchHardContainerToolConfig(),)
    dataset_container: str = chz.field(default="orngcresco")
    dataset_id: str = "data.damajercak.swe.upload05202025.rcr_12878.train_hq"
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_swebenchhard_multistage_grader, scenario="rkld")
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_fn_coreutils)
            ),
        )
    )
    max_num_yields: int = 256
    variant_producer: VariantProducer | None = override(DeepSWEVardiscProducer)


@chz.chz
class SWEBenchHardRepairVSCTrainDatasetConfig(SWEBenchHardRepairTrainDatasetConfig, IsOverride):
    tool_configs: tuple[ToolConfig, ...] = override(_make_vsc_tool_configs)
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="repair",
            enable_vsc_func_enforcement_grader=True,
        )
    )
    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardVSCCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_vscutils)
            ),
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                "prbot_msft.data_customization.vsc_conversation_init:conversation_init_fn"
            ),
            replace_problem_with_user_prompt_wrapper(),
            enforce_vsc_commands(),
        )
    )
    model_identity_str: str = 'You are an AI programming assistant.\nWhen asked for your name, you must respond with "GitHub Copilot".\n'
    instructions: str = "Follow the user's requirements carefully & to the letter.\nFollow Microsoft content policies.\nAvoid content that violates copyrights.\nIf you are asked to generate content that is harmful, hateful, racist, sexist, lewd, or violent, only respond with \"Sorry, I can't assist with that.\"\nKeep your answers short and impersonal.\n<instructions>\nYou are a highly sophisticated automated coding agent with expert-level knowledge across many different programming languages and frameworks.\nThe user will ask a question, or ask you to perform a task, and it may require lots of research to answer correctly. There is a selection of tools that let you perform actions or retrieve helpful context to answer the user's question.\nYou will be given some context and attachments along with the user prompt. You can use them if they are relevant to the task, and ignore them if not.\nIf you can infer the project type (languages, frameworks, and libraries) from the user's query or the context that you have, make sure to keep them in mind when making changes.\nIf the user wants you to implement a feature and they have not specified the files to edit, first break down the user's request into smaller concepts and think about the kinds of files you need to grasp each concept.\nIf you aren't sure which tool is relevant, you can call multiple tools. You can call tools repeatedly to take actions or gather as much context as needed until you have completed the task fully. Don't give up unless you are sure the request cannot be fulfilled with the tools you have. It's YOUR RESPONSIBILITY to make sure that you have done all you can to collect necessary context.\nPrefer using the semantic_search tool to search for context unless you know the exact string or filename pattern you're searching for.\nDon't make assumptions about the situation- gather context first, then perform the task or answer the question.\nThink creatively and explore the workspace in order to make a complete fix.\nDon't repeat yourself after a tool call, pick up where you left off.\nNEVER print out a codeblock with file changes unless the user asked for it.\nNEVER print out a codeblock with a terminal command to run unless the user asked for it. Use the run_in_terminal tool instead.\nYou don't need to read a file if it's already provided in context.\n</instructions>\n<toolUseInstructions>\nWhen using a tool, follow the json schema very carefully and make sure to include ALL required properties.\nAlways output valid JSON when using a tool.\nIf a tool exists to do a task, use the tool instead of asking the user to manually take an action.\nIf you say that you will take an action, then go ahead and use the tool to do it. No need to ask permission.\nNever use multi_tool_use.parallel or any tool that does not exist. Use tools using the proper procedure, DO NOT write out a json codeblock with the tool inputs.\nNEVER say the name of a tool to a user. For example, instead of saying that you'll use the run_in_terminal tool, say \"I'll run the command in a terminal\".\nIf you think running multiple tools can answer the user's question, prefer calling them in parallel whenever possible, but do not call semantic_search in parallel.\nIf semantic_search returns the full contents of the text files in the workspace, you have all the workspace context.\nDon't call the run_in_terminal tool multiple times in parallel. Instead, run one command and wait for the output before running the next command.\nAfter you have performed the user's task, if the user corrected something you did, expressed a coding preference, or communicated a fact that you need to remember, use the update_user_preferences tool to save their preferences.\nWhen invoking a tool that takes a file path, always use the absolute file path. If the file has a scheme like untitled: or vscode-userdata:, then use a URI with the scheme.\n</toolUseInstructions>\n<applyPatchInstructions>\nTo edit files in the workspace, you can use the apply_patch tool.\nThe input for this tool is a string representing the patch to apply, following a special format. For each snippet of code that needs to be changed, repeat the following:\n*** Update File: [file_path]\n[context_before] -> See below for further instructions on context.\n-[old_code] -> Precede each line in the old code with a minus sign.\n+[new_code] -> Precede each line in the new, replacement code with a plus sign.\n[context_after] -> See below for further instructions on context.\n\nFor instructions on [context_before] and [context_after]:\n- By default, show 3 lines of code immediately above and 3 lines immediately below each change. If a change is within 3 lines of a previous change, do NOT duplicate the first change's [context_after] lines in the second change's [context_before] lines.\n- If 3 lines of context is insufficient to uniquely identify the snippet of code within the file, use the @@ operator to indicate the class or function to which the snippet belongs.\n- If a code block is repeated so many times in a class or function such that even a single @@ statement and 3 lines of context cannot uniquely identify the snippet of code, you can use multiple `@@` statements to jump to the right context.\nYou must use the same indentation style as the original code. If the original code uses tabs, you must use tabs. If the original code uses spaces, you must use spaces.\n\nSee below for an example of the patch format. If you propose changes to multiple regions in the same file, you should repeat the *** Update File header for each snippet of code to change:\n\n*** Begin Patch\n*** Update File: /Users/<USER>/pygorithm/searching/binary_search.py\n@@ class BaseClass\n@@ \\\\tdef method():\n[3 lines of pre-context]\n-[old_code]\n+[new_code]\n+[new_code]\n[3 lines of post-context]\n*** End Patch\n\nNEVER print this out to the user, instead call the tool and the edits will be applied and shown to the user.\n\n</applyPatchInstructions>\n<outputFormatting>\nUse proper Markdown formatting in your answers. When referring to a filename or symbol in the user's workspace, wrap it in backticks.\n<example>\nThe class `Person` is in `src/models/person.ts`.\n</example>\n\n</outputFormatting>"


@chz.chz
class SWEBenchHardRepairVSCTrainDatasetConfig_O3Grader(
    SWEBenchHardRepairVSCTrainDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="repair",
            enable_vsc_func_enforcement_grader=True,
            topic_mode_or_user=O3_TOPIC_MODE_OR_USER,
            topic_or_snapshot=O3_TOPIC_OR_SNAPSHOT,
            bus_renderer=O3_BUS_RENDERER,
            bus_redis_id=O3_BUS_REDIS_ID,
        )
    )


@chz.chz
class SWEBenchHardRepairVSCEvalDatasetConfig(SWEBenchHardRepairVSCTrainDatasetConfig, IsOverride):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="repair",
            enable_vsc_func_enforcement_grader=False,
        )
    )


def _make_tool_configs(
    container_tool_config: str = "padawan_tool",
    internet_policy: str = "no_access",
) -> tuple[ToolConfig, ...]:
    if container_tool_config == "padawan_tool":
        return (PadawanToolConfig(internet_policy=internet_policy),)
    elif container_tool_config == "mix_tool":
        return (MixToolConfig(internet_policy=internet_policy),)
    else:
        raise ValueError(f"Unknown padawan tool config: {container_tool_config}")


@chz.chz()
class SweBenchHardPDWContainerResourceConfig(SweBenchHardCaasContainerResourceConfig):
    caas_endpoint: str = CAAS_ENDPOINT
    caas_container_image: str = CAAS_IMAGE

    @tenacity.retry(
        wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
        stop=tenacity.stop_after_attempt(10),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
        reraise=True,
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        return await super().initialize_resource(dp, volume_mounts=PDW_MNT)


@chz.chz()
class SweBenchHardMixTCaasContainerResourceConfig(SweBenchHardCaasContainerResourceConfig):
    @tenacity.retry(
        wait=tenacity.wait_random_exponential(min=2, max=32, multiplier=2),
        stop=tenacity.stop_after_attempt(10),
        before_sleep=tenacity.before_sleep_log(
            logger,  # type: ignore
            logging.WARNING,
        ),
        reraise=True,
    )
    async def initialize_resource(self, dp: datapoint.HarmonyDatapoint) -> CaasContainer:
        return await super().initialize_resource(dp, volume_mounts=VSC_MNT)


@chz.chz
class SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader(
    SWEBenchHardRepairTrainDatasetConfig, IsOverride
):
    """SWE-Bench Hard v1 with Padawan v2 tools."""

    resource_configs: tuple[ResourceConfig, ...] = chz.field(
        default=(
            SweBenchHardPDWContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_fn_padawan_v2),
                enable_network_after_setup=ENABLE_NETWORK,
            )
            if not MIX_VSC_TOOL
            else SweBenchHardMixTCaasContainerResourceConfig(
                setup_fn=cast(SweBenchHardSetupFn, setup_fn_padawan_v2),
                enable_network_after_setup=ENABLE_NETWORK,
            ),
        )
    )
    tool_configs: tuple[ToolConfig, ...] = override(
        partial(
            _make_tool_configs,
            container_tool_config="padawan_tool",
            internet_policy="enabled" if ENABLE_NETWORK else "no_access",
        ),
    )
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="repair",
            use_padawan_enforcement_grader=True,
            topic_mode_or_user=O3_TOPIC_MODE_OR_USER,
            topic_or_snapshot=O3_TOPIC_OR_SNAPSHOT,
            bus_renderer=O3_BUS_RENDERER,
            bus_redis_id=O3_BUS_REDIS_ID,
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
            get_padawan_system_prompt(),
            conversation_converter(
                func_name="prbot_msft.configs.swebench_hard:padawan_conversation_init_fn_wrapper",
            ),
            replace_problem_with_user_prompt_wrapper(),
        )
    )
    instructions: str = ""
    model_identity_str: str = PADAWAN_MODEL_IDENTITY


@chz.chz
class SWEBenchHardTestTrainWithSilverPadawanDatasetConfig(
    SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_test_multistage_grader,
            use_padawan_enforcement_grader=True,
            is_execution_grader=False,
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
            get_padawan_system_prompt(include_build_style_for_easy_only=True),
            conversation_converter(
                func_name="prbot_msft.configs.swebench_hard:padawan_noexec_test_conversation_init_fn_wrapper",
            ),
            replace_problem_with_user_prompt_wrapper(),
        )
    )
    instructions: str = ""


@chz.chz
class SWEBenchHardTestTrainExecPadawanDatasetConfig(
    SWEBenchHardTestTrainWithSilverPadawanDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_swebenchhard_test_multistage_grader, is_execution_grader=True)
    )


@chz.chz
class SWEBenchHardTestTrainORPadawanDatasetConfig(
    SWEBenchHardTestTrainWithSilverPadawanDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        make_swebenchhard_test_multistage_or_grader
    )


@chz.chz
class SWEBenchHardRepairTrainPadawanDatasetConfig(
    SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="repair",
            use_padawan_enforcement_grader=False,
        )
    )


@chz.chz
class SWEBenchHardRepairRKLDTrainPadawanDatasetConfig(
    SWEBenchHardRepairTrainPadawanDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="rkld",
            use_padawan_enforcement_grader=False,
        )
    )


@chz.chz
class SWEBenchHardRepairTrainPadawanWithSilverDatasetConfig(
    SWEBenchHardRepairTrainPadawanDatasetConfig, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        make_swebenchhard_multistage_silver_grader
    )


@chz.chz
class SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig(
    SWEBenchHardRepairTrainPadawanWithSilverDatasetConfig, IsOverride
):
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
            get_padawan_system_prompt(include_build_style_for_easy_only=True),
            conversation_converter(
                func_name="prbot_msft.configs.swebench_hard:padawan_noexec_conversation_init_fn_wrapper",
            ),
            replace_problem_with_user_prompt_wrapper(),
        )
    )
    instructions: str = ""


@chz.chz
class SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig_O3Grader(
    SWEBenchHardRepairTrainPadawanNoExecWithSilverDatasetConfig, IsOverride
):
    """SWE-Bench Hard V2 with Padawan v2 tools."""

    # use_padawan_enforcement_grader=True has to be paired with get_padawan_system_prompt
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_silver_grader,
            topic_mode_or_user=O3_TOPIC_MODE_OR_USER,
            topic_or_snapshot=O3_TOPIC_OR_SNAPSHOT,
            bus_renderer=O3_BUS_RENDERER,
            bus_redis_id=O3_BUS_REDIS_ID,
            use_padawan_enforcement_grader=True,
        )
    )


@chz.chz
class SWEBenchHardRepairEvalPadawanDatasetConfig(
    SWEBenchHardRepairTrainPadawanDatasetConfig, IsOverride
):
    # For eval, no func enforcement grader
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="repair",
            use_padawan_enforcement_grader=False,
        )
    )

    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            conversation_converter(
                func_name="prbot_msft.configs.swebench_hard:padawan_conversation_init_fn_wrapper",
            ),
            replace_problem_with_user_prompt_wrapper(),
        )
    )


@chz.chz
class SWEBenchHardRepairEvalPadawanOrDatasetConfig(
    SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(make_swebenchhard_multistage_or_grader)


@chz.chz
class SWEBenchHardReproDatasetConfig(SWEBenchHardRepairTrainDatasetConfig, IsOverride):
    dataset_id: str = "data.jadhuang.the5636mre.the5636mre.train"
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(make_swebenchhard_multistage_grader, scenario="repro")
    )


@chz.chz
class SWEBenchHardReproTrainPadawanDatasetConfig(
    SWEBenchHardRepairTrainPadawanDatasetConfig_O3Grader, IsOverride
):
    grader: Grader[HarmonyCompletionDatapoint] = override(
        partial(
            make_swebenchhard_multistage_grader,
            scenario="repro",
            topic_mode_or_user=O3_TOPIC_MODE_OR_USER,
            topic_or_snapshot=O3_TOPIC_OR_SNAPSHOT,
            bus_renderer=O3_BUS_RENDERER,
            bus_redis_id=O3_BUS_REDIS_ID,
        )
    )
    datapoint_converters: tuple[FunctionWrapper, ...] = chz.field(
        default=(
            random_tool_set(),
            conversation_converter(
                "prbot_msft.data_customization.repro_conversation_init:conversation_init_fn"
            ),
            replace_problem_with_user_prompt_wrapper(),
        )
    )
    model_identity_str: str = REPRO_TASK_GOAL
