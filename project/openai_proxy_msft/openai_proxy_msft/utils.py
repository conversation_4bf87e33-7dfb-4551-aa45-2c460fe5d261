from typing import Any, Dict, List, Optional

from functions import parse_function


def convert_openai_tools_to_tools_section(tools: Optional[List[Dict[str, Any]]]) -> Dict[str, str]:
    """Convert OpenAI format tools to internal tools_section format."""
    if not tools:
        return {}

    tools_section = {}

    for tool in tools:
        func = tool.get("function") if isinstance(tool, dict) else None
        name = func.get("name") if isinstance(func, dict) else None

        if isinstance(name, str):
            try:
                tools_section[name] = parse_function(func).to_typescript()
            except Exception:
                continue

    return tools_section
