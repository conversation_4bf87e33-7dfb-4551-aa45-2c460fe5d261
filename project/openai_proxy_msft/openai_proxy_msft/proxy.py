#!/usr/bin/env python
"""
FastAPI proxy service for all OpenAI compatible clients to support local model API calls.

This service provides an OpenAI-compatible API interface that internally uses
BusTokenCompleter to communicate with local model engines running in pods.

Usage:
    python proxy.py

The service will start on http://localhost:8500/v1 and provide:
    - POST /v1/chat/completions (OpenAI compatible)
"""

import json
import logging
import time
import uuid
from typing import Any, AsyncGenerator, Dict, List, Optional

from bus_token_completer import BusTokenCompleter, QoSType
from chat import chat

# Import required components from the existing codebase
from chat.render.renderer_registry import get_renderer
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from message_completer.message_completer import ParseErrorMode
from message_completer.token_message_completer import TokenMessageCompleter
from openai_proxy_msft.utils import convert_openai_tools_to_tools_section
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# OpenAI API compatible models
class ToolCall(BaseModel):
    id: str = Field(..., description="The ID of the tool call")
    type: str = Field(default="function", description="The type of the tool call")
    function: Dict[str, Any] = Field(..., description="The function that the model called")


class ChatMessage(BaseModel):
    role: str = Field(..., description="The role of the message author")
    content: Optional[str] = Field(default=None, description="The content of the message")
    tool_calls: Optional[List[ToolCall]] = Field(
        default=None, description="The tool calls generated by the model"
    )
    tool_call_id: Optional[str] = Field(
        default=None, description="Tool call that this message is responding to"
    )


class ChatCompletionRequest(BaseModel):
    model: str = Field(..., description="ID of the model to use")
    messages: List[ChatMessage] = Field(
        ..., description="A list of messages comprising the conversation"
    )
    tools: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="A list of tools the model may call"
    )
    tool_choice: Optional[str] = Field(
        default=None, description="Controls which (if any) tool is called by the model"
    )
    stream: bool = Field(default=False, description="Whether to stream back partial progress")
    temperature: float = Field(default=1.0, description="Sampling temperature")
    max_tokens: Optional[int] = Field(
        default=None, description="Maximum number of tokens to generate"
    )
    top_p: float = Field(default=1.0, description="Nucleus sampling parameter")
    n: int = Field(default=1, description="Number of chat completion choices to generate")
    stop: Optional[List[str]] = Field(
        default=None, description="Up to 4 sequences where the API will stop generating"
    )


class ChatCompletionChoice(BaseModel):
    index: int
    message: Optional[ChatMessage] = None
    delta: Optional[Dict[str, Any]] = None
    finish_reason: Optional[str] = None


class ChatCompletionUsage(BaseModel):
    prompt_tokens: int
    completion_tokens: int
    total_tokens: int


class ChatCompletionResponse(BaseModel):
    id: str
    object: str = "chat.completion"
    created: int
    model: str
    choices: List[ChatCompletionChoice]
    usage: Optional[ChatCompletionUsage] = None


class ChatCompletionStreamResponse(BaseModel):
    id: str
    object: str = "chat.completion.chunk"
    created: int
    model: str
    choices: List[ChatCompletionChoice]


# FastAPI app
app = FastAPI(
    title="OpenAI Proxy",
    description="OpenAI-compatible API for local model engines",
    version="1.0.0",
)


class OpenAIProxy:
    """Main proxy service class that handles OpenAI API compatibility."""

    def __init__(self):
        # Cache for initialized components per configuration
        self._component_cache = {}

    def _get_components(self, model: str):
        """Get or create components for the given configuration."""
        topic, renderer = parse_model(model)
        cache_key = f"{topic}:{renderer}"

        if cache_key not in self._component_cache:
            try:
                # Get the renderer
                renderer_obj = get_renderer(renderer)
                logger.info(f"Initialized renderer: {renderer}")

                # Configure BusTokenCompleter
                bus_tc_config = BusTokenCompleter.Config(
                    topic_or_snapshot=topic,
                    qos_type=QoSType.ROUND_ROBIN_BY_POD,
                )

                # Configure TokenMessageCompleter
                completion_params = {"temperature": 1.0}

                message_completer_config = TokenMessageCompleter.Config(
                    token_completer_config=bus_tc_config,
                    completion_params=completion_params,
                    renderer=renderer_obj,
                )

                # Build the message completer
                message_completer = message_completer_config.build()

                self._component_cache[cache_key] = {
                    "renderer": renderer_obj,
                    "message_completer": message_completer,
                }

                logger.info(f"Successfully initialized components for {cache_key}")

            except Exception as e:
                logger.error(f"Failed to initialize components for {cache_key}: {e}")
                raise

        return self._component_cache[cache_key]

    def _extract_content_and_recipient(self, chat_message):
        """Extract content from a chat message, handling different content types."""
        try:
            content = getattr(chat_message, "content", None)
            if content is not None and content.strip():
                return content, "all"
            tool_calls = getattr(chat_message, "tool_calls", None)
            if tool_calls and len(tool_calls) > 0:
                first_tool_call = tool_calls[0]
                function = getattr(first_tool_call, "function", None)
                if function and isinstance(function, dict):
                    arguments = function.get("arguments", "")
                    if arguments:
                        return arguments, function.get("name", "all")
            return "", "all"
        except (AttributeError, TypeError, IndexError):
            return "", "all"

    def _find_author_name(self, tool_call_id, messages):
        for msg in messages:
            if not msg.tool_calls:
                continue
            for tool_call in msg.tool_calls:
                if tool_call.id == tool_call_id:
                    return tool_call.function.get("name")
        return None

    def _convert_openai_messages_to_conversation(
        self, messages: List[ChatMessage], tools: Optional[List[Dict[str, Any]]] = None
    ) -> chat.Conversation:
        """Convert OpenAI format messages to internal conversation format."""
        chat_messages = []

        # Handle system message separately - use the first system message or create default
        system_message = None
        user_messages = []

        for msg in messages:
            if msg.role == "system" and system_message is None:
                system_message = msg.content
            elif msg.role in ["user", "assistant", "tool"]:
                user_messages.append(msg)

        # Create system message with proper format
        if system_message is None:
            system_message = "You are ChatGPT, a large language model trained by OpenAI."

        # Convert tools to tools_section format
        tools_section = convert_openai_tools_to_tools_section(tools)

        chat_messages.append(
            chat.Message.system(
                model_identity_desc=system_message,
                tools_section=tools_section,
                channel_config=chat.SystemChannelConfig(
                    valid_channels=("analysis", "final"), channel_required=True
                ),
                metadata=chat.SystemContentMetadata(reward_multiplier=1.0),
            )
        )

        # Add user and assistant messages
        for msg in user_messages:
            if msg.role == "user":
                chat_messages.append(chat.Message.user(msg.content))
            elif msg.role == "assistant":
                content, recipient = self._extract_content_and_recipient(msg)
                chat_messages.append(chat.Message.assistant(content=content, recipient=recipient))
            elif msg.role == "tool":
                author_name = self._find_author_name(msg.tool_call_id, user_messages)
                chat_messages.append(chat.Message.tool(msg.content, author_name))

        # Create conversation
        convo = chat.Conversation(messages=chat_messages)
        logger.info(f"convo: {convo}")

        # Set budget overrides
        convo.metadata.header_yields_budget_total_override = 256
        convo.metadata.header_yields_budget_for_action = 256

        return convo

    def _extract_content_text(self, message) -> str:
        """Extract text content from a message, handling different content types."""
        if hasattr(message, "content") and message.content:
            return str(message.content)
        return ""

    def _parse_messages_for_tool_calls(self, messages) -> tuple[str, List[ToolCall]]:
        """Parse messages to extract content and tool calls."""
        content_parts = []
        tool_calls = []

        for message in messages:
            # Check if this is a tool call message (recipient != 'all')
            if hasattr(message, "recipient") and message.recipient and message.recipient != "all":
                # This is a tool call message
                message_id = getattr(message, "id", str(uuid.uuid4()))
                content_text = self._extract_content_text(message)

                tool_call = ToolCall(
                    id=str(message_id),
                    type="function",
                    function={"name": message.recipient, "arguments": content_text},
                )
                tool_calls.append(tool_call)
            else:
                # Regular content message
                content_text = self._extract_content_text(message)
                if content_text:
                    content_parts.append(content_text)

        return " ".join(content_parts), tool_calls

    async def _generate_completion(
        self, request: ChatCompletionRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Generate completion using BusTokenCompleter."""
        try:
            # Get components for this configuration
            components = self._get_components(request.model)
            message_completer = components["message_completer"]

            # Convert OpenAI messages to internal format
            convo = self._convert_openai_messages_to_conversation(request.messages, request.tools)

            # Generate completion
            completion = await message_completer.async_completion(
                conversations=[convo], n=request.n, seed=0, end_header=True
            )

            choice = completion.choices[0]
            messages = choice.get_messages(parse_error_mode=ParseErrorMode.SYSTEM_ERROR)

            logger.info(f"Raw message: {messages}")

            # Parse messages for content and tool calls
            content, tool_calls = self._parse_messages_for_tool_calls(messages)

            # Determine finish reason
            finish_reason = "tool_calls" if tool_calls else "stop"

            # Yield the complete response data
            yield {
                "content": content if content and not tool_calls else None,
                "tool_calls": tool_calls if tool_calls else None,
                "finish_reason": finish_reason,
            }

        except Exception as e:
            logger.error(f"Error generating completion: {e}")
            raise HTTPException(status_code=500, detail=f"Completion generation failed: {str(e)}")


def parse_model(model: str) -> tuple[str, str]:
    """Parse model string in format 'topic$renderer'.

    Note: The topic parameter should be provided without the 'az://' prefix.

    Example:
    - Input: 'bus:snap:orngcresco/twapi/mini/e/yunshengli-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-rrbif-v1/policy/step_000300:user:xuga$harmony_v4.0.16'
    - Output: ('bus:snap:orngcresco/twapi/mini/e/yunshengli-mix15-pdw2-ev3-mixed-itc-spi32-o4mini-tef03-5-tpm1-rm-lr1e-5-rrbif-v1/policy/step_000300:user:xuga', 'harmony_v4.0.16')
    """
    try:
        # Split from the right to get renderer first
        if "$" not in model:
            raise ValueError(f"Invalid model format. Expected 'topic$renderer', got '{model}'")

        # Find the last colon to separate renderer
        last_colon_idx = model.rfind("$")
        if last_colon_idx == -1:
            raise ValueError(f"Invalid model format. Expected 'topic$renderer', got '{model}'")

        renderer = model[last_colon_idx + 1 :]
        topic = model[:last_colon_idx]

        # Validate that none of the parts are empty
        if not topic or not renderer:
            raise ValueError(
                f"All model parameters must be non-empty. Got topic='{topic}', renderer='{renderer}'"
            )

        # Clean up whitespace
        topic = topic.strip()
        renderer = renderer.strip()

        logger.info(f"Parsed model parameters - topic: {topic}, renderer: {renderer}")

        return topic, renderer

    except Exception as e:
        logger.error(f"Failed to parse model parameters '{model}': {e}")
        raise HTTPException(status_code=400, detail=f"Invalid model parameters: {model}")


# Initialize the proxy service
proxy = OpenAIProxy()


@app.get("/")
async def root():
    """Health check endpoint."""
    return {"status": "ok", "service": "openai-proxy", "version": "1.0.0"}


@app.get("/v1/models")
async def list_models():
    """List available models (OpenAI compatible)."""
    return {
        "object": "list",
        "data": [
            {
                "id": "bus",
                "object": "model",
                "created": int(time.time()),
                "owned_by": "openai-proxy",
            }
        ],
    }


@app.post("/v1/chat/completions")
async def create_chat_completion(request: ChatCompletionRequest):
    """Create a chat completion (OpenAI compatible)."""
    completion_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
    created = int(time.time())

    logger.info(f"user: {request.messages[-1]}")

    if request.stream:
        return StreamingResponse(
            _stream_chat_completion(completion_id, created, request),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            },
        )
    else:
        return await _non_stream_chat_completion(completion_id, created, request)


async def _stream_chat_completion(
    completion_id: str,
    created: int,
    request: ChatCompletionRequest,
):
    """Generate streaming chat completion response."""
    try:
        # Send initial chunk with role
        initial_chunk = ChatCompletionStreamResponse(
            id=completion_id,
            created=created,
            model=request.model,
            choices=[
                ChatCompletionChoice(index=0, delta={"role": "assistant"}, finish_reason=None)
            ],
        )
        yield f"data: {initial_chunk.model_dump_json()}\n\n"

        # Get completion data
        response_data = None
        async for data in proxy._generate_completion(request):
            response_data = data
            break  # We get all data in one go now

        if response_data:
            # Send tool calls if available
            if response_data.get("tool_calls"):
                tool_calls_chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionChoice(
                            index=0,
                            delta={
                                "tool_calls": [
                                    tc.model_dump() for tc in response_data["tool_calls"]
                                ]
                            },
                            finish_reason=None,
                        )
                    ],
                )
                yield f"data: {tool_calls_chunk.model_dump_json()}\n\n"

            # Send content if available
            elif response_data.get("content"):
                content_chunk = ChatCompletionStreamResponse(
                    id=completion_id,
                    created=created,
                    model=request.model,
                    choices=[
                        ChatCompletionChoice(
                            index=0, delta={"content": response_data["content"]}, finish_reason=None
                        )
                    ],
                )
                yield f"data: {content_chunk.model_dump_json()}\n\n"

            # Send final chunk with finish reason
            final_chunk = ChatCompletionStreamResponse(
                id=completion_id,
                created=created,
                model=request.model,
                choices=[
                    ChatCompletionChoice(
                        index=0, delta={}, finish_reason=response_data.get("finish_reason", "stop")
                    )
                ],
            )
        else:
            # Fallback final chunk
            final_chunk = ChatCompletionStreamResponse(
                id=completion_id,
                created=created,
                model=request.model,
                choices=[ChatCompletionChoice(index=0, delta={}, finish_reason="stop")],
            )

        yield f"data: {final_chunk.model_dump_json()}\n\n"
        yield "data: [DONE]\n\n"

    except Exception as e:
        logger.error(f"Error in streaming completion {completion_id}: {e}", exc_info=True)
        error_chunk = {
            "error": {"message": str(e), "type": "server_error", "code": "internal_error"}
        }
        yield f"data: {json.dumps(error_chunk)}\n\n"


async def _non_stream_chat_completion(
    completion_id: str,
    created: int,
    request: ChatCompletionRequest,
):
    """Generate non-streaming chat completion response."""
    try:
        # Get completion data
        response_data = None
        async for data in proxy._generate_completion(request):
            response_data = data
            break  # We get all data in one go now

        if not response_data:
            response_data = {"content": "", "tool_calls": None, "finish_reason": "stop"}

        # Create the response message
        message = ChatMessage(
            role="assistant",
            content=response_data.get("content"),
            tool_calls=response_data.get("tool_calls"),
        )

        response = ChatCompletionResponse(
            id=completion_id,
            created=created,
            model=request.model,
            choices=[
                ChatCompletionChoice(
                    index=0,
                    message=message,
                    finish_reason=response_data.get("finish_reason", "stop"),
                )
            ],
            usage=ChatCompletionUsage(
                prompt_tokens=0,  # TODO: Calculate actual token counts
                completion_tokens=0,
                total_tokens=0,
            ),
        )

        return response

    except Exception as e:
        logger.error(f"Error in non-streaming completion: {e}")
        raise HTTPException(status_code=500, detail=str(e))
