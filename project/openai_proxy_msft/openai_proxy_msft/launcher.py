"""Launcher for the OpenAI Proxy Service."""

import argparse
import os
import subprocess
import sys
from pathlib import Path

STORAGE_ACCOUNT = "orngscuscresco"


def check_proxy_script():
    """Check if the proxy script exists."""
    script_dir = Path(__file__).parent
    proxy_path = script_dir / "proxy.py"
    if not proxy_path.exists():
        print(f"\033[0;31mError: proxy.py not found in {script_dir}\033[0m")
        sys.exit(1)
    return script_dir


def run():
    """Run the OpenAI Proxy Service."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="OpenAI Proxy Service")
    parser.add_argument(
        "--background", action="store_true", help="Run service in background (like nohup)"
    )
    args = parser.parse_args()

    print("\033[0;32mStarting OpenAI Proxy Service...\033[0m")

    host = "0.0.0.0"
    port = "8500"
    log_level = "info"

    print(f"Host: {host}")
    print(f"Port: {port}")
    print(f"Log Level: {log_level}")

    # Perform checks
    script_dir = check_proxy_script()

    print(f"Script Directory: {script_dir}")

    # Set PYTHONPATH to include the current directory
    env = os.environ.copy()
    current_pythonpath = env.get("PYTHONPATH", "")
    if current_pythonpath:
        env["PYTHONPATH"] = f"{script_dir}:{current_pythonpath}"
    else:
        env["PYTHONPATH"] = str(script_dir)

    print("\033[0;32mStarting proxy service...\033[0m")
    print(f"Service will be available at: http://{host}:{port}")
    print(f"OpenAI-compatible endpoint: http://{host}:{port}/v1/chat/completions")
    print("")
    print("\033[1;33mTo configure your clients to use this proxy, set:\033[0m")
    print(f'base_url = "http://localhost:{port}/v1"')
    print("")
    print("Press Ctrl+C to stop the service")
    print("")

    # Change to script directory and start the service
    os.chdir(script_dir)

    # Build uvicorn command
    cmd = [
        sys.executable,
        "-m",
        "uvicorn",
        "proxy:app",
        "--host",
        host,
        "--port",
        port,
        "--log-level",
        log_level,
        "--no-access-log",
        "--reload",
    ]

    if args.background:
        # Run in background like nohup
        print("\033[1;33mStarting service in background...\033[0m")
        log_file = script_dir / "openai_proxy.log"
        print(f"Logs will be written to: {log_file}")

        try:
            with open(log_file, "w") as f:
                process = subprocess.Popen(
                    cmd, env=env, stdout=f, stderr=subprocess.STDOUT, cwd=script_dir
                )
            print(f"✓ Service started in background with PID: {process.pid}")
            print(f"To stop the service, run: kill {process.pid}")
            print(f"To view logs, run: tail -f {log_file}")
        except Exception as e:
            print(f"\033[0;31mError starting background service: {e}\033[0m")
            sys.exit(1)
    else:
        # Run in foreground (original behavior)
        try:
            subprocess.run(cmd, env=env, check=True)
        except KeyboardInterrupt:
            print("\n\033[1;33mService stopped by user\033[0m")
        except subprocess.CalledProcessError as e:
            print(f"\033[0;31mError starting service: {e}\033[0m")
            sys.exit(1)


if __name__ == "__main__":
    run()
