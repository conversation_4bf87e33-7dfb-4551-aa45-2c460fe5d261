"""This is a prompt postprocess that converts TypeScript-format tools_section into a JSON schema tools section.

Useful for opensource models like Qwen.

It is a minimal implementation that only supports basic TypeScript types. If you hit exceptions, please update the code.

Usage example:
...dataset.prompt_postprocessors.2=deep_swe_msft.tangerine.prompt_postprocessors:JsonSchemaToolsSectionPromptPostprocessor

"""
import json
import re

import chat
import chz
from berry import Datapoint, PromptT, prompt_postprocessor
from qstar.common.datapoint import HarmonyCompletionDatapoint


def _convert_tools_section_to_json_schema(tools_section: dict[str, str]) -> dict[str, str]:
    """ Converts TypeScript-format tools_section into a JSON schema tools section.

    Args:
        tools_section: The original tools section in TypeScript format.

    Returns:
        A dictionary with function names as keys and JSON schema strings as values.
    """
    result = {}
    
    for key, ts_content in tools_section.items():
        if key == "functions":
            # Parse TypeScript namespace content
            # Extract type definitions with their preceding comments
            
            # Split content into lines for easier processing
            lines = ts_content.split('\n')
            current_comments = []
            i = 0
            
            while i < len(lines):
                line = lines[i].strip()
                
                # Collect comments
                if line.startswith('//'):
                    current_comments.append(line[2:].strip())
                    i += 1
                    continue
                
                # Check if this is a type definition
                type_match = re.match(r'type\s+(\w+)\s*=\s*\(_:\s*\{', line)
                if type_match:
                    func_name = type_match.group(1)
                    description = ' '.join(current_comments) if current_comments else ""
                    
                    # Parse the parameter block
                    param_lines = []
                    i += 1
                    brace_count = 1
                    
                    while i < len(lines) and brace_count > 0:
                        param_line = lines[i]
                        for char in param_line:
                            if char == '{':
                                brace_count += 1
                            elif char == '}':
                                brace_count -= 1
                        
                        if brace_count > 0:
                            param_lines.append(param_line)
                        i += 1
                    
                    # Parse parameters from the collected lines
                    properties = {}
                    required = []
                    param_content = '\n'.join(param_lines)
                    
                    # Extract parameter definitions with their comments
                    param_pattern = r'//\s*(.+?)\n(\w+)(\?)?\s*:\s*([^,\n]+)(?:,|\n|$)'
                    param_matches = re.finditer(param_pattern, param_content, re.DOTALL)
                    
                    for param_match in param_matches:
                        param_description = param_match.group(1).strip()
                        param_name = param_match.group(2)
                        is_optional = param_match.group(3) == '?'
                        param_type = param_match.group(4).strip()
                        
                        # Convert TypeScript types to JSON Schema types
                        json_type = _convert_ts_type_to_json_schema(param_type)
                        json_type["description"] = param_description
                        
                        properties[param_name] = json_type
                        
                        if not is_optional:
                            required.append(param_name)
                    
                    # Also handle parameters without preceding comments
                    param_only_pattern = r'^(\w+)(\?)?\s*:\s*([^,\n]+)(?:,|\n|$)'
                    for line in param_lines:
                        line = line.strip()
                        if line and not line.startswith('//'):
                            param_match = re.match(param_only_pattern, line)
                            if param_match and param_match.group(1) not in properties:
                                param_name = param_match.group(1)
                                is_optional = param_match.group(2) == '?'
                                param_type = param_match.group(3).strip()
                                
                                json_type = _convert_ts_type_to_json_schema(param_type)
                                properties[param_name] = json_type
                                
                                if not is_optional:
                                    required.append(param_name)
                    
                    # Build JSON schema in the new format
                    parameters = {
                        "type": "object",
                        "properties": properties
                    }
                    
                    if required:
                        parameters["required"] = required
                    
                    schema = {
                        "type": "function",
                        "function": {
                            "name": f"functions.{func_name}",
                            "parameters": parameters
                        }
                    }
                    
                    if description:
                        schema["function"]["description"] = description
                    
                    result[f"functions.{func_name}"] = json.dumps(schema)
                    current_comments = []
                else:
                    # Reset comments if we encounter a non-comment, non-type line
                    if line and not line.startswith('//'):
                        current_comments = []
                    i += 1
    
    return result


def _convert_ts_type_to_json_schema(ts_type: str) -> dict:
    """ Converts a TypeScript type to JSON Schema format.
    
    Args:
        ts_type: TypeScript type string
        
    Returns:
        JSON Schema type definition
    """
    ts_type = ts_type.strip()
    
    if ts_type == "string":
        return {"type": "string"}
    elif ts_type == "number":
        return {"type": "number"}
    elif ts_type == "boolean":
        return {"type": "boolean"}
    elif ts_type.endswith("[]"):
        # Array type
        item_type = ts_type[:-2]
        return {
            "type": "array",
            "items": _convert_ts_type_to_json_schema(item_type)
        }
    else:
        # Default to string for unknown types
        return {"type": "string"}


@chz.chz
class JsonSchemaToolsSectionPromptPostprocessor(prompt_postprocessor.PromptPostprocessor):
    """ A post-processor that converts TypeScript-format tools_section into a JSON schema tools section.

    This is useful for opensource models that requires JSON schema tools section in the prompt.
    """

    def postprocess_prompt(
        self,
        dp: Datapoint[PromptT],
        prompt: PromptT,
    ) -> PromptT:
        assert isinstance(dp, HarmonyCompletionDatapoint) and isinstance(prompt, chat.Conversation)

        # Find the last system message in the prompt
        last_sys_msg = None
        for i in range(len(prompt.messages)):
            if isinstance(prompt.messages[-i].content, chat.SystemContent):
                last_sys_msg = prompt.messages[-i]
                break
        assert last_sys_msg is not None, "No system message found in the prompt"

        last_sys_msg.content.tools_section = _convert_tools_section_to_json_schema(last_sys_msg.content.tools_section)

        return prompt