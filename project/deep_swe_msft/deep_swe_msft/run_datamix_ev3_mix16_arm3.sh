# for i in {0..47}; do b restart -p damajercak-mix16-a1-0722-rollout-worker-w$i; done
dt=`date '+%Y%m%d-%H%M%S'`
EXPERIMENT_NAME="mix16-arm-3-pdw2-ev3-mixed-itc-spi32-gpt5-mini-sft-1e-4-469-100steps-tef03-5-tpm1-rm-lr1e-5-run-$dt"

# Enforcement settings for datasets 0-6
ENFORCE_PROB=0.3
ENFORCE_FREQ=5
JUICE=128

CMD=(
beam python --use-cwd -m qstar.run_experiment nostrict
name=$EXPERIMENT_NAME
# name=pdw2-test-sbh-run1
seed=20250724

skip_validate_config=True

# Policy settings
:berry_models.scallion_lpe:d36_80g_mbg16_bf16_chicken
# :berry_models.scallion_lpe:d36_80g_mbg16_bf16
root_config='mini.root.dev driver_rpc_timeout=600 dedicated_driver_node=False'
policy.ml_config='twberry.scallion_lpe.finetune ema_horizon=None allow_embedding_prefix_loading=True twapi_driver_actors=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 ignore_parent_dataset_state=True infrequent_eval_datasets= final_eval_datasets= tensorcache_v2_load_allow_missing_tensor=True tensorcache_v2_fetch_diskcache_with_ib=false nccl_ib_timeout=22'
# policy.sampling_ml_config='ixf_cross_request_logprob_moe_caching=False ixf_kv_block_unit_size=512 ixf_sampling_gumbel_noise_fp64=True ixf_use_applied_comms=True enable_tensorcache_v2=True tensorcache_v2_indirect_load_threshold=0 include_audio_embeddings=False do_multimodal_projections_in_ixf_batch_runner=False decoupled_attn=False decoupled_attention_token_mapping=None embedding_tensors_redis_cache_ttl=15000 tensorcache_v2_fetch_diskcache_with_ib=false mem_disable_second_allocator=True'

# gpt-5-mini
# policy.initial_checkpoint="az://orngscuscresco/twapi/mini/e/zhendongwang-swe_workflow_sft_gpt5-mini_lr1e-4-run4/30915951-67fb-4155-b35d-f550831018d9/checkpoint/model1/000000000469/"
policy.initial_checkpoint=az://orngscuscresco/twapi/mini/e/damajercak-mix16-arm-3-pdw2-ev3-mixed-itc-spi32-gpt5-mini-sft-1e-4-469-100steps-tef03-5-tpm1-rm-lr2e-6-run-20250801-221026/policy/step_000250/
policy.initial_checkpoint_mode=resume
policy.n_gpus=128
policy.is_multimodal=True
...harmony_renderer_name=harmony_v4.0.16_berry_v3_1mil_orion_no_budget_commentary_cs_cross_msg_msc

# Systems settings
:qstar.presets.common:longer_timeout
peashooter.sampling_use_ev3=True
peashooter.timeout_seconds.stalled_datapoint=7200
timeout.default=None

# Model settings
policy.n_ctx=131072
defaults.n_ctx=131072
...container_tool_config="mix_tool"
...sampler='deep_swe_msft.padawan_data.sampler:PadawanSampler'
...harmony_constrained_sampling=True
# defaults.sample_completer="deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter"
optimizer.hparam_scaler.lr_per_instance_d16=1e-5
...save_every=5

# Dataset configs!
:deep_swe_msft.presets:train_padawan_v2_mix16_arm_3
...dataset_container=orngscuscresco
...max_num_yields=256
...override_pass_rate_minimum=0.001
# ...override_reward_multiplier=${JUICE}

berry_curriculum=berry_curriculums.MultiEpochCurriculum
berry_curriculum.max_epochs=10

# IPB/SPI
# NOTE: Default IPB/SPI is 64/64, this is lower for faster derisks (assuming 20 rollout workers)
defaults.inverse_token_cost=1024
defaults.instances_per_batch=32
defaults.target_samples_per_instance=32

# Enable the COMMENTARY channel
defaults.channel_config.channels="analysis,final"

batch_completer.n_batches_in_flight=30 # number of batches in flight
peashooter.num_sampling_processes=32 # number of sampling processes per instance worker
peashooter.sampling_concurrency=16 # concurrency sampling threads per process
peashooter.num_instance_workers=64 # number of instance workers

# Dataset configs
defaults.instance_completer=qstar.instance_completers:VariantsInstanceCompleter
defaults.instance_completer.instance_optimizer=qstar.instance_optimizers:VariantsInstanceOptimizer

# swe-bench-train
batcher.curriculum.training_datasets.0.dataset.instance_completer="deep_swe_msft.swe_bench_train_v2_padawan_v2.dataset_config:SWEBenchV2InstanceCompleter"
batcher.curriculum.training_datasets.0.dataset.inverse_token_cost_multiplier=16
batcher.curriculum.training_datasets.0.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
batcher.curriculum.training_datasets.0.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
batcher.curriculum.training_datasets.0.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ

# webdev task
batcher.curriculum.training_datasets.1.dataset.inverse_token_cost_multiplier=16
batcher.curriculum.training_datasets.1.dataset.override_target_samples_per_instance=8
batcher.curriculum.training_datasets.1.dataset.override_pass_rate_minimum=1e-6
batcher.curriculum.training_datasets.2.dataset.inverse_token_cost_multiplier=16
batcher.curriculum.training_datasets.2.dataset.override_target_samples_per_instance=8
batcher.curriculum.training_datasets.2.dataset.override_pass_rate_minimum=1e-6

# setup task - bgdb
batcher.curriculum.training_datasets.3.dataset.inverse_token_cost_multiplier=8
batcher.curriculum.training_datasets.3.dataset.override_target_samples_per_instance=64
# setup task - repo setup
batcher.curriculum.training_datasets.4.dataset.inverse_token_cost_multiplier=8
# setup task - dependency
batcher.curriculum.training_datasets.5.dataset.inverse_token_cost_multiplier=8

# swe-bench-hard-v2 repair
batcher.curriculum.training_datasets.6.dataset.inverse_token_cost_multiplier=16
batcher.curriculum.training_datasets.6.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
batcher.curriculum.training_datasets.6.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
batcher.curriculum.training_datasets.6.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ

# swe-bench-hard-v1 repair
batcher.curriculum.training_datasets.7.dataset.inverse_token_cost_multiplier=16
batcher.curriculum.training_datasets.7.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
batcher.curriculum.training_datasets.7.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
batcher.curriculum.training_datasets.7.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ

# swe-bench-hard-v1 repair exec - python
batcher.curriculum.training_datasets.8.dataset.inverse_token_cost_multiplier=16
batcher.curriculum.training_datasets.8.dataset.sample_completer='deep_swe_msft.padawan_data.sampler:PadawanSampleCompleter'
batcher.curriculum.training_datasets.8.dataset.sample_completer.enforce_prob=$ENFORCE_PROB
batcher.curriculum.training_datasets.8.dataset.sample_completer.enforce_freq=$ENFORCE_FREQ

...dataset.variant_producer=CompositeVariantProducer
...dataset.variant_producer.variant_producers.0=VarDiscountingVariantProducer
...dataset.variant_producer.variant_producers.0.override_reward_multiplier=${JUICE}

# Logging/misc
security_profile=msft-orng
github_upload=False
wandb_enable=True
wandb.wandb_project=swe-main-run
kafka_enable=False
enable_slackbot=False
)

"${CMD[@]}" 2>&1 | tee -a swe-nv4.log
