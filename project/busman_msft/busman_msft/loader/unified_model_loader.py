"""Unified model data loader that combines OMOM and Model Registry sources."""

import logging
from typing import List

from model_registry.model_data import BaseModelData, BerryModelData
from model_registry.model_feature import ModelFeatures
from model_registry.registry_utils import find_all_model_paths, get_model_data
from omom_msft.manager import ModelManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_registry_models(ignore_errors: bool = True) -> List[BaseModelData]:
    """
    Load all available Model Registry models.
    Use find_all_model_paths to get the list of available models,
    and then use get_model_data to get ModelData instances.

    Args:
        ignore_errors: whether to ignore errors and continue processing other models

    Returns:
        List of BaseModelData objects from Model Registry
    """
    registry_models = []

    try:
        # Get all available model paths using find_all_model_paths
        package_name = "model_registry_msft"
        model_paths = find_all_model_paths(package_name=package_name)
        logger.info(f"Found {len(model_paths)} available models")

        for model_path, class_name in model_paths:
            try:
                # We are out of the OpenAI's default model_regsitry,
                # here we must add the package_name ("model_registry_msft")
                # to the model name to get the model correctly loaded.
                model_data = get_model_data(f"{package_name}.{model_path}")
                model_data._source = "model_registry"
                registry_models.append(model_data)
                logger.info(f"successfully loaded model {model_path} ({class_name})")
            except Exception as e:
                logger.error(f"failed to load model {model_path}: {e}")
                if not ignore_errors:
                    raise e

    except Exception as e:
        logger.error(f"failed to find model paths: {e}")
        if not ignore_errors:
            raise e

    logger.info(f"Loaded {len(registry_models)} Model Registry models")
    return registry_models


def convert_omom_config_to_model_data(model_name: str, model_config: dict) -> BaseModelData:
    """Convert OMOM model configuration to BerryModelData format.

    Args:
        model_name: Name of the model
        model_config: OMOM model configuration dictionary

    Returns:
        BerryModelData object
    """
    # Parse extra_config_string into args and kwargs
    extra_args = []
    extra_kwargs = {}

    extra_config_string = (
        getattr(model_config, "extra_config_string", "")
        if hasattr(model_config, "extra_config_string")
        else ""
    )

    if extra_config_string:
        config_parts = extra_config_string.split()
        for part in config_parts:
            if "=" in part:
                key, value = part.split("=", 1)
                # Try to convert to appropriate type
                if value.lower() in ("true", "false"):
                    extra_kwargs[key] = value.lower() == "true"
                elif value.isdigit():
                    extra_kwargs[key] = int(value)
                elif value.replace(".", "").replace("-", "").isdigit():
                    extra_kwargs[key] = float(value)
                else:
                    extra_kwargs[key] = value
            else:
                extra_args.append(part)

    # Create features if multimodal
    features = None
    is_multimodal = getattr(model_config, "is_multimodal", False)
    if is_multimodal:
        encoder_decoder_snapshots = getattr(model_config, "encoder_decoder_snapshots", None)
        features = ModelFeatures(
            is_multimodal=True, encoder_decoder_snapshots=encoder_decoder_snapshots
        )

    # Currently we only create BerryModelData, we will support more type soon
    if features:
        model_data = BerryModelData(
            name=model_name,
            external_name=model_name,
            snapshot_path=getattr(model_config, "snapshot_path", ""),
            renderer_name=getattr(model_config, "renderer_name", ""),
            extra_args=extra_args,
            extra_kwargs=extra_kwargs,
            features=features,
        )
    else:
        model_data = BerryModelData(
            name=model_name,
            external_name=model_name,
            snapshot_path=getattr(model_config, "snapshot_path", ""),
            renderer_name=getattr(model_config, "renderer_name", ""),
            extra_args=extra_args,
            extra_kwargs=extra_kwargs,
        )
    model_data._source = "omom"

    return model_data


def load_omom_models() -> List[BaseModelData]:
    """Load all available OMOM models and convert to BaseModelData format.

    Returns:
        List of BaseModelData objects from OMOM
    """
    # The models are already in model_registry_msft, but their are slight differences in their names
    # We need to filter them out here
    filtered_models = [
        "03-0402-410",
        "03-mini",
    ]

    try:
        manager = ModelManager()
        omom_model_names = manager.list_models()

        omom_models = []
        for model_name in omom_model_names:
            # Skip models in the filter list
            if model_name in filtered_models:
                logger.info(f"Skipping filtered model: {model_name}")
                continue

            try:
                model_config = manager.get_model(model_name)
                if model_config:
                    model_data = convert_omom_config_to_model_data(model_name, model_config)
                    omom_models.append(model_data)
                else:
                    logger.warning(f"Could not load OMOM model config for: {model_name}")
            except Exception as e:
                logger.error(f"Error loading OMOM model '{model_name}': {e}")
                continue

        logger.info(f"Loaded {len(omom_models)} OMOM models")
        return omom_models

    except Exception as e:
        logger.error(f"Error loading OMOM models: {e}")
        return []


def deduplicate_models(
    models: List[BaseModelData], priority_source: str = "model_registry"
) -> List[BaseModelData]:
    """Remove duplicate models, keeping the one from the priority source.

    Args:
        models: List of BaseModelData objects
        priority_source: Which source to prioritize ("model_registry" or "omom")

    Returns:
        Deduplicated list of BaseModelData objects
    """
    model_map = {}

    for model in models:
        model_name = getattr(model, "name", "")
        source = getattr(model, "_source", "unknown")

        if model_name not in model_map:
            model_map[model_name] = model
        else:
            # Replace if current model is from priority source
            existing_source = getattr(model_map[model_name], "_source", "unknown")
            if source == priority_source and existing_source != priority_source:
                model_map[model_name] = model

    deduplicated = list(model_map.values())
    logger.info(f"Deduplicated {len(models)} models to {len(deduplicated)} models")

    return deduplicated


def load_all_models(
    enable_deduplication: bool = True, priority_source: str = "model_registry"
) -> List[BaseModelData]:
    """Load all models from both OMOM and Model Registry sources.

    Args:
        enable_deduplication: Whether to remove duplicate models
        priority_source: Which source to prioritize when deduplicating

    Returns:
        Unified list of BaseModelData objects
    """
    logger.info("Loading models from all sources...")

    # Load from both sources
    # Update: All the models in omom have migrated to model registry.
    # So we don't need to load from omom from now on.
    omom_models = []  # load_omom_models()
    registry_models = load_registry_models()

    # Combine all models
    all_models = omom_models + registry_models

    logger.info(
        f"Total models before deduplication: {len(all_models)} "
        f"(OMOM: {len(omom_models)}, Registry: {len(registry_models)})"
    )

    # Apply deduplication if enabled
    if enable_deduplication:
        all_models = deduplicate_models(all_models, priority_source)

    logger.info(f"Final model count: {len(all_models)}")

    return all_models
