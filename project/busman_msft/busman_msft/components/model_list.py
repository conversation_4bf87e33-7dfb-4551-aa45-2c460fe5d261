"""Model list component for the Bus Model Manager."""

from typing import List, Optional

import streamlit as st


class ModelList:
    """A component for displaying a list of models with selection capability."""

    def __init__(self):
        """Initialize the ModelList component."""
        self._inject_css()

    def _inject_css(self):
        """Inject custom CSS for styling the model list."""
        st.markdown(
            """
            <style>
            /* Theme variables */
            :root {
                --selected-border: #0068c9;
                --hover-bg: rgba(0, 104, 201, 0.1); /* 10% transparent accent */
                --selected-bg: rgba(0, 104, 201, 0.15); /* 15% transparent accent */
            }
            
            /* Custom button styling */
            div[data-testid="stButton"] button {
                width: 100%;
                text-align: left;
                padding: 12px 16px;
                border: none;
                border-radius: 0;
                background: transparent;
                font-size: 16px;
                display: flex;
                flex-direction: column;
                gap: 4px;
                transition: all 0.2s ease;
                justify-content: flex-start;
                align-items: flex-start;
            }

            div[data-testid="stButton"] button:hover {
                background-color: var(--hover-bg);
            }
            
            /* Selected state styling */
            div[data-testid="stButton"] button[kind="primary"] {
                background-color: var(--selected-bg);
                border-left: 4px solid var(--selected-border);
                color: inherit !important; /* Ensure text color is inherited */
            }
            </style>
        """,
            unsafe_allow_html=True,
        )

    def render(self, models: List[str], selected_model: Optional[str] = None) -> Optional[str]:
        """Render the model list.

        Args:
            models: List of model names to display
            selected_model: Currently selected model name

        Returns:
            The selected model name if changed, None otherwise
        """
        # Create a container for the list
        list_container = st.container()

        # Render each model as a button with custom styling
        with list_container:
            for model in models:
                # Get snapshot info from session state
                snapshot = st.session_state.get(f"snapshot_{model}", "")

                # Create a button with custom styling
                if st.button(
                    f"{model}\n{snapshot}",
                    key=f"model_{model}",
                    use_container_width=True,
                    type="secondary" if model != selected_model else "primary",
                ):
                    return model

        return None
