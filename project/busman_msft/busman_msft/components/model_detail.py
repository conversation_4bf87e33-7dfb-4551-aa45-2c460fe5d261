"""Model detail component for displaying model information."""

import streamlit as st
from bus_metrics_msft.collector.bus_metrics import BusMetricsCollector
from bus_metrics_msft.models.metrics import TopicInfo
from model_registry.model_data import BaseModelData


def render(model: BaseModelData):
    """Render the model details section.

    Args:
        model: BaseModelData instance containing model information
    """
    if not model:
        st.error("No model provided.")
        return

    # All models should now be BaseModelData instances
    if not isinstance(model, BaseModelData):
        st.error(f"Invalid model type: {type(model)}. Expected BaseModelData.")
        return

    # Render the unified model data
    render_model_data(model)

    # Render topic and pool information
    st.write("---")
    render_topic_pool_section(model)


def render_model_data(model: BaseModelData):
    """Render unified model data details.

    Args:
        model: BerryModelData or BaseModelData instance containing model information
    """
    st.write(f"## {getattr(model, 'name', 'Unknown Model')}")

    st.write("### Model Configuration")
    col_basic_info, col_advanced_info = st.columns(2)

    with col_basic_info:
        st.write("**Model Name**")
        st.caption(model.name or "N/A")

        st.write("**External Name**")
        st.caption(model.external_name or "N/A")

        st.write("**Snapshot Path**")
        st.caption(model.snapshot_path or "N/A")

        st.write("**Renderer**")
        st.caption(model.renderer_name or "N/A")

        st.write("**Type**")
        st.caption("Multimodal" if model.features.is_multimodal else "Text-only")

        st.write("**Source**")
        st.caption(
            "[model_registry](https://dev.azure.com/project-argos/Mimco/_git/glass?path=/lib/model_registry/model_registry_msft/model_registry_msft/chatberry/msft.py)"
        )

    with col_advanced_info:
        # Extra args
        extra_args = getattr(model, "extra_args", [])
        st.write("**Extra Arguments**")
        with st.expander(f"{len(extra_args)} items", expanded=False):
            if extra_args:
                for arg in extra_args:
                    st.caption(f"• {arg}")
            else:
                st.caption("No extra arguments")

        # Extra kwargs
        extra_kwargs = getattr(model, "extra_kwargs", {})
        st.write("**Extra Kwargs**")
        with st.expander(f"{len(extra_kwargs)} items", expanded=False):
            if extra_kwargs:
                for key, value in extra_kwargs.items():
                    st.caption(f"• **{key}**: {value}")
            else:
                st.caption("No extra kwargs")

        # Features (if available)
        features = getattr(model, "features", None)
        if features:
            st.write("**Model Features**")
            with st.expander("Features", expanded=False):
                if hasattr(features, "is_multimodal"):
                    st.caption(f"• **Multimodal**: {features.is_multimodal}")
                if hasattr(features, "encoder_decoder_snapshots"):
                    st.caption(f"• **Encoder-Decoder**: {features.encoder_decoder_snapshots}")
                # render other features
                for attr in dir(features):
                    if not attr.startswith("_") and attr not in [
                        "is_multimodal",
                        "encoder_decoder_snapshots",
                    ]:
                        value = getattr(features, attr)
                        if not callable(value):
                            st.caption(f"• **{attr}**: {value}")


def render_topic_pool_section(model: BaseModelData):
    """
    Render the topic and pool information section.

    Args:
        model: ModelConfig instance containing model information
    """
    st.write("### Available Topics")

    try:
        # Initialize the metrics collector with default Prometheus client
        bus_metrics_collector = BusMetricsCollector()

        # Load model metrics
        with st.spinner("Loading topic and pool data..."):
            metrics = bus_metrics_collector.load_model_metrics(model.snapshot_path)

        if not metrics.has_data:
            st.info("No active topics found for this model")
            return

        # Render each topic
        for i, topic_info in enumerate(metrics.sorted_topics, 1):
            _render_topic_expander(topic_info, topic_number=i)

    except Exception as e:
        st.error(f"Error loading topic data: {e}")


def _render_topic_expander(topic_info: TopicInfo, topic_number: int):
    """Render individual topic expander with improved UX."""
    topic = topic_info.topic
    total_engines = len(topic_info.engines)
    completion_rate = topic_info.completion_rate

    # Simple topic header - just the number
    st.markdown(
        f"️🚏 Topic #{topic_number}: :gray[{total_engines} engines, {completion_rate:.2f} req/s]"
    )

    # Topic name - full width since no buttons needed
    st.caption(f":green[{topic}]")

    # Engine details expander
    with st.expander("Engine details", expanded=False):
        _render_engine_table(topic_info)

        # Use the grafana_url property from TopicInfo
        st.markdown(f"📊 [View Grafana Dashboard]({topic_info.grafana_url})")


def _render_engine_table(topic_info: TopicInfo):
    """Render the engine/pool data table."""
    engines = topic_info.engines

    if engines:
        # Convert to DataFrame for display
        import pandas as pd

        # Prepare data for display with visual indicators for inactive engines and cluster info
        display_data = []
        for engine in engines:
            display_data.append(
                {
                    "Cluster": engine.cluster,
                    "Engine Owner": engine.user,
                    "Engine Pool Name": engine.pool,
                    "Request Rate (req/s)": f"{engine.completion_rate:.2f}",
                }
            )

        df = pd.DataFrame(display_data)
        st.dataframe(df, use_container_width=True, hide_index=True)

        # Summary statistics
        all_users = list(set(engine.user for engine in engines))

        if len(all_users) <= 5:
            users_display = ", ".join(sorted(all_users))
        else:
            users_display = ", ".join(sorted(all_users)[:5]) + f", +{len(all_users)-5} more"

        st.caption(f"Total {len(engines)} engines across {len(all_users)} users - {users_display}")
    else:
        st.info("No engines found for this topic")
