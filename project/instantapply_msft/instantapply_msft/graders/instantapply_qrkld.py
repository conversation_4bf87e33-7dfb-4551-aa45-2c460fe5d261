import math
import re
from typing import Sequence

import chat
import chz
import structlog
from bus.registry import DEFAULT_BUS_LINE
from bus_token_completer.bus_token_completer import BusTokenCompleter
from chat.render import get_renderer
from multimodal_token.toklist.list import TokList
from qstar.common import datapoint, types, utils
from qstar.graders import grader
from qstar.graders.rkld_grader import RkldGrader
from qstar.sample_completers import sample_completer

from .instantapply import SolutionComparisonGrader
from .multi_teacher_rkld import MultiTeacherRkldGrader

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class SolutionComparisonGraderMultiTeacherRKLD(SolutionComparisonGrader):
    """
    SolutionComparisonGrader with Multi-Teacher RKLD functionality.
    Uses a MultiTeacherRkldGrader instance to compute RKLD logprobs and attaches them to the grading results.
    """

    rkld_grader: MultiTeacherRkldGrader = chz.field(
        doc="RKLD grader to use for logprob aggregation."
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        raise NotImplementedError()


@chz.chz(typecheck=True)
class SubstringSolutionComparisonGrader(SolutionComparisonGrader):
    def _strip_markdown_code_block(self, text: str) -> str:
        """
        Strips the markdown code block from the given text.
        Returns the code inside the last code block if present, else returns None.
        Handles language specifiers with spaces and optional newlines after the language.
        """
        import re

        # Match all code blocks in markdown format, allowing language names with spaces and optional newlines
        pattern = re.compile(r"```(?:[^\n]*)?\n*(.*?)\n?```", re.DOTALL)
        matches = pattern.findall(text.strip())
        if matches:
            return matches[-1]
        return None

    def _clean_given_answer(self, given_answer):
        return self._strip_markdown_code_block(given_answer)


@chz.chz(typecheck=True)
class SolutionComparisonGraderRKLD(SubstringSolutionComparisonGrader):
    """
    SolutionComparisonGrader with single-teacher RKLD functionality.
    Uses a RkldGrader instance to compute RKLD logprobs and attaches them to the grading results.
    """

    rkld_grader: RkldGrader = chz.field(doc="RKLD grader to use for logprob aggregation.")

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        rkld_samples = self.rkld_grader._grade_batch_inner(
            samples, sample_execution_context, profile_label
        )
        graded_samples = super()._grade_batch_inner(
            samples, sample_execution_context, profile_label
        )

        # Manually add teacher_logp from rkld_samples to graded_samples
        merged_samples = [
            graded.with_grade(
                log_rewards={**graded.log_rewards, **rkld.log_rewards},
                is_correct=graded.is_correct and rkld.is_correct,  # rkld.is_correct is always True
                given_answer=getattr(graded, "given_answer", None),
                telemetry_metadata=getattr(graded, "telemetry_metadata", None),
                teacher_logp=rkld.teacher_logp,
            )
            for graded, rkld in zip(graded_samples, rkld_samples)
        ]
        return merged_samples
