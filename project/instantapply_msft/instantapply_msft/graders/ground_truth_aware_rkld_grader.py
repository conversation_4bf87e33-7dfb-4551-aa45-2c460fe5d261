# Inspired by this code:
# https://dev.azure.com/project-argos/Mimco/_git/glass?path=/project/prbot_msft/prbot_msft/graders/swebenchhard_double_rkld_grader.py&version=GCcd75df2c129812bb8497747324acb35d951ed5db&_a=blame


from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Sequence

import chz
import numpy as np
import numpy.typing as npt

# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #
import structlog
import tiktoken
from bus.registry import DEFAULT_BUS_LINE
from bus_token_completer.bus_token_completer import BusTokenCompleter
from multimodal_token.toklist import IntTokSpan, TokList
from oaicommon.oai_types import none_throws
from qstar.common import datapoint, types
from qstar.graders import grader as grader_module
from qstar.graders.rkld_grader import RkldGrader
from qstar.sample_completers import sample_completer

logger = structlog.stdlib.get_logger(component=__name__)

from datetime import datetime

LOG_FILENAME = "/var/log/supervisor/gta_rkld_grader_results.log"


def log_to_file_sync(message: str, log_file: str = LOG_FILENAME):
    """Logs a message to the specified log file with a timestamp."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(log_file, "a") as f:
        f.write(f"[{timestamp}] {message}\n")


def log_wrapper(message):
    logger.info(f"!prd GroundTruthAwareRkldGrader: {message}")
    # log_to_file_sync(f"!prd GroundTruthAwareRkldGrader: {message}", LOG_FILENAME)


# # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # # #


@dataclass
class HintInjector:
    def __post_init__(self):
        """
        Initializes the HintInjector. This method can be overridden by subclasses to perform
        additional initialization tasks.
        """
        self.cargo = {}

    @abstractmethod
    def inject_hint(
        self, sample: types.SampleWithCompletion[datapoint.MathgenDatapoint], hint_text: str = None
    ) -> TokList:
        """
        Injects the hint text into the prompt before grading.
        This method should be implemented by subclasses to provide the actual hint text.
        """
        raise NotImplementedError("Subclasses must implement this method.")

    @abstractmethod
    def strip_hint(self, teacher_logprobs: npt.NDArray[np.float32]) -> npt.NDArray[np.float32]:
        """
        Strips the hint logprobs from the sample's prompt tokens.
        This method should be implemented by subclasses to provide the actual stripping logic.
        """
        raise NotImplementedError("Subclasses must implement this method.")


class HintInBeginning(HintInjector):
    """
    A hint injector that injects the hint text at the beginning of the prompt.
    """

    def inject_hint(
        self, sample: types.SampleWithCompletion[datapoint.MathgenDatapoint], hint_tokens: TokList
    ) -> TokList:
        """
        Injects the hint text at the beginning of the prompt before grading.
        Stores prompt_tokens and sample.tokens for later use in strip_hint.
        """
        assert sample.prompt_tokens is not None, "Prompt tokens must be set."
        self.cargo["prompt_tokens"] = sample.prompt_tokens
        self.cargo["sample_tokens"] = sample.tokens
        self.cargo["hint_len"] = len(hint_tokens)
        # [hint | prompt | sample]
        return TokList.concatenate([hint_tokens, sample.prompt_tokens, sample.tokens])

    def strip_hint(self, teacher_logprobs: npt.NDArray[np.float32]) -> npt.NDArray[np.float32]:
        """
        Strips the hint logprobs from the sample's prompt tokens.
        Removes the logprobs corresponding to the hint tokens at the beginning.
        Assumes the first token has already been discarded.
        """
        hint_len = self.cargo["hint_len"]
        prompt_len = len(self.cargo["prompt_tokens"])
        sample_len = len(self.cargo["sample_tokens"])
        # teacher_logprobs: [hint | prompt | sample] (first token already discarded)
        # Remove the first `hint_len` logprobs
        kept = teacher_logprobs[hint_len:]
        return kept


class HintInMiddle(HintInjector):
    """
    A hint injector that injects the hint text in the middle of the prompt.
    This is useful for cases where the hint is a superset of the solution.
    """

    def inject_hint(
        self, sample: types.SampleWithCompletion[datapoint.MathgenDatapoint], hint_tokens: TokList
    ) -> TokList:
        """
        Injects the hint text into the prompt before grading.
        Stores prompt_tokens and sample.tokens for later use in strip_hint.
        """
        assert sample.prompt_tokens is not None, "Prompt tokens must be set."
        self.cargo["prompt_tokens"] = sample.prompt_tokens
        self.cargo["sample_tokens"] = sample.tokens
        return TokList.concatenate([sample.prompt_tokens, hint_tokens, sample.tokens])

    def strip_hint(self, teacher_logprobs: npt.NDArray[np.float32]) -> npt.NDArray[np.float32]:
        """
        Strips the hint logprobs from the sample's prompt tokens.
        Removes the logprobs corresponding to the hint tokens in the middle.
        Assumes the first token has already been discarded.
        """
        prompt_len = len(self.cargo["prompt_tokens"])
        sample_len = len(self.cargo["sample_tokens"])
        # teacher_logprobs: [prompt | hint | sample] (first token already discarded)
        hint_len = len(teacher_logprobs) - prompt_len - sample_len
        # Keep [0:prompt_len] and [prompt_len+hint_len:]
        kept = np.concatenate(
            [teacher_logprobs[:prompt_len], teacher_logprobs[prompt_len + hint_len + 1 :]]
        )
        return kept


@chz.chz(typecheck=True)
class GroundTruthAwareRkldGrader(RkldGrader):

    hint_text: str = chz.field(
        doc="Hint text to be added to the prompt before grading. This should be a string that will be formatted with the `ground_truth`.",
        default="Hint: The following is a superset of the solution\n{ground_truth}\n",
    )

    ground_truth_key: str = chz.field(
        doc="The key in the sample's ground truth datapoint metadata that contains the ground truth.",
    )

    hint_injector_cls: type[HintInjector] = chz.field(
        default=HintInBeginning,
        doc="A HintInjector class that defines how to inject and strip hints from the samples.",
    )

    encoding_name: str = chz.field(
        default="orion_200k",
        doc="The name of the encoding to use for the hint tokens. This should match the encoding used by the model.",
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        """
        Main entry point for grading a batch of samples. We'll fetch logprobs from the
        bus endpoint and attach them to each sample's additional_metadata.
        """
        log_wrapper(
            f"Grading {len(samples)=} samples with hint text: {self.hint_text=}, {self.ground_truth_key=}"
        )
        if self._any_sample_is_multimodal(samples):
            raise ValueError("Multimodal samples have not been tested for RKLD grader.")
        graded_samples = []

        for sample in samples:
            log_wrapper(f"Sample to grade: {sample=}")
            assert sample.prompt_tokens is not None, "Prompt tokens must be set."

            hint_injector = self.hint_injector_cls()

            # Get the hint tokens to be injected into the prompt
            hint_tokens = self._get_hint_tokens(sample)

            # Inject the hint tokens into the sample's prompt tokens
            tokens_for_teacher = hint_injector.inject_hint(sample, hint_tokens=hint_tokens)
            log_wrapper(
                f"{len(sample.prompt_tokens)=}, {len(hint_tokens)=}, {len(sample.tokens)=}, {len(tokens_for_teacher)=}"
            )

            # Fetch the logprobs for the sample with the hint injected
            teacher_logp = self._fetch_logprobs(tokens_for_teacher)
            log_wrapper(f"Fetched logprobs: {len(teacher_logp)=}, {teacher_logp=}")

            # Strip the hint logprobs from the sample's prompt tokens
            teacher_logp = hint_injector.strip_hint(teacher_logprobs=teacher_logp)
            log_wrapper(f"Stripped hint logprobs: {len(teacher_logp)=}, {teacher_logp=}")

            graded_samples.append(
                sample.with_grade(
                    log_rewards={"neg_rkld": 0},
                    is_correct=self._has_no_model_errors(sample),
                    teacher_logp=teacher_logp,
                )
            )
        log_wrapper(f"Samples graded.")
        return graded_samples

    def _get_hint_tokens(self, sample) -> TokList:
        """
        Returns the hint tokens to be added to the prompt before grading.
        This is a placeholder method that can be overridden in subclasses.
        """
        encoding = tiktoken.get_encoding(self.encoding_name)

        log_wrapper(f"Sample metadata: {sample.gt_datapoint.metadata=}")
        ground_truth = sample.gt_datapoint.metadata.get(self.ground_truth_key, None)

        log_wrapper(f"Ground truth extracted: {ground_truth=}")
        assert ground_truth is not None, "Gold patch must be set"

        hint_text = self.hint_text.format(ground_truth=ground_truth)
        hint_tokens = TokList(spans=[IntTokSpan.from_ints(encoding.encode(hint_text))])
        return hint_tokens
