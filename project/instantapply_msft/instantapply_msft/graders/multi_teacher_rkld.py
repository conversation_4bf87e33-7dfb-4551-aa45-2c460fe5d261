from typing import Sequence

import chz
from qstar.common import datapoint, types
from qstar.graders.grader import Grader
from qstar.graders.rkld_grader import RkldGrader
from qstar.sample_completers import sample_completer


@chz.chz(typecheck=True)
class MultiTeacherRkldGrader(Grader[datapoint.MathgenDatapoint]):
    """
    A grader that applies multiple RkldGraders to each sample and collects their logprobs.

    Example usage in Blueprint:
    grader=outlook_msft.graders.multi_teacher_rkld:MultiTeacherRkldGrader
    grader.graders.0=qstar.graders.rkld_grader:RkldGrader
    grader.graders.0.topic=teacher_model_v1
    grader.graders.0.topic_mode_or_user=default
    grader.graders.1=qstar.graders.rkld_grader:RkldGrader
    grader.graders.1.topic=teacher_model_v2
    grader.graders.1.topic_mode_or_user=default
    """

    graders: tuple[RkldGrader, ...] = chz.field(doc="List of RkldGraders to use for grading.")
    aggregation: str = chz.field(
        doc="""
        Aggregation method for teacher logprobs. Options:
        - 'mean': Arithmetic mean. Use when you want the average teacher opinion.  
          $\\mathrm{agg}_t = \\frac{1}{N} \\sum_{i=1}^N t_i$
        - 'sum': Sum of logprobs. Use for multiplicative combination in log space.  
          $\\mathrm{agg}_t = \\sum_{i=1}^N t_i$
        - 'median': Median value. Use for robustness to outliers.  
          $\\mathrm{agg}_t = \\mathrm{median}(t_1, ..., t_N)$
        - 'min': Minimum value. Use for pessimistic/worst-case aggregation.  
          $\\mathrm{agg}_t = \\min_i t_i$
        - 'max': Maximum value. Use for optimistic/best-case aggregation.  
          $\\mathrm{agg}_t = \\max_i t_i$
        - 'weighted_mean': Weighted average. Use when some teachers are more trusted.  
          $\\mathrm{agg}_t = \\sum_{i=1}^N w_i t_i$ where $\\sum w_i = 1$
        - 'geometric_mean': Geometric mean in probability space. Use for multiplicative averaging.  
          $\\mathrm{agg}_t = \\log \\left( \\left( \\prod_{i=1}^N \\exp(t_i) \\right)^{1/N} \\right)$
        """,
        default="mean",
    )
    weights: list[float] | None = chz.field(
        doc="Weights for weighted_mean aggregation. Must sum to 1. Only used if aggregation='weighted_mean'.",
        default=None,
    )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        # For each grader, get their grades
        all_teacher_grades = []
        for grader in self.graders:
            teacher_grades = grader._grade_batch_inner(
                samples, sample_execution_context, profile_label
            )
            all_teacher_grades.append(teacher_grades)

        # Merge the results: for each sample, collect all teacher_logps
        merged_samples = []
        for idx, sample in enumerate(samples):
            teacher_logps = [
                teacher_grades[idx].teacher_logp for teacher_grades in all_teacher_grades
            ]
            import numpy as np

            teacher_logps_np = [np.array(tlp) for tlp in teacher_logps]
            # Aggregation logic
            if self.aggregation == "mean":
                aggregated_logp = np.mean(teacher_logps_np, axis=0)
            elif self.aggregation == "sum":
                aggregated_logp = np.sum(teacher_logps_np, axis=0)
            elif self.aggregation == "median":
                aggregated_logp = np.median(teacher_logps_np, axis=0)
            elif self.aggregation == "min":
                aggregated_logp = np.min(teacher_logps_np, axis=0)
            elif self.aggregation == "max":
                aggregated_logp = np.max(teacher_logps_np, axis=0)
            elif self.aggregation == "weighted_mean":
                if not self.weights or len(self.weights) != len(teacher_logps_np):
                    raise ValueError(
                        "weights must be provided and match number of teachers for weighted_mean"
                    )
                weights = np.array(self.weights)
                weights = weights / weights.sum()  # normalize
                stacked = np.stack(teacher_logps_np, axis=0)
                aggregated_logp = np.tensordot(weights, stacked, axes=([0], [0]))
            elif self.aggregation == "geometric_mean":
                stacked = np.stack(teacher_logps_np, axis=0)
                aggregated_logp = np.log(np.exp(stacked).mean(axis=0))
            else:
                raise ValueError(f"Unknown aggregation method: {self.aggregation}")
            # Use the first grader's grade as base
            base_grade = all_teacher_grades[0][idx]
            metadata = dict(base_grade.metadata)
            metadata["teacher_logps"] = teacher_logps
            metadata["aggregation"] = self.aggregation
            merged_sample = base_grade.with_grade(
                log_rewards=base_grade.log_rewards,
                is_correct=base_grade.is_correct,
                teacher_logp=aggregated_logp,
                metadata=metadata,
            )
            merged_samples.append(merged_sample)
        return merged_samples
