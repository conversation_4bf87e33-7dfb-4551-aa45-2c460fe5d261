import math
import re
from typing import Sequence

import chat
import chz
import structlog
from bus.registry import DEFAULT_BUS_LINE
from bus_token_completer.bus_token_completer import BusTokenCompleter
from chat.render import get_renderer
from multimodal_token.toklist.list import TokList
from qstar.common import datapoint, types, utils
from qstar.graders import grader
from qstar.sample_completers import sample_completer

logger = structlog.stdlib.get_logger(component=__name__)


@chz.chz(typecheck=True)
class AnswerCorrectnessGrader(grader.Grader[datapoint.MathgenDatapoint]):
    """
    Grader that extracts a score from the completion, compares it to the ground truth answer.
    The sample is correct only if both the score matches. Rejects the sample if no score is found.

    Expects data in PSA data format:
    # Problem\n\n" + text + "\n# Solution:\n\n" + reasoning_trace + "\n# Answer\n\n" + answer
    """

    # MESSAGE_START_PATTERN: str = "<|im_start|>assistant<|im_sep|>"
    # Support for all MESSAGE_START_PATTERNs:
    # - <|im_start|>assistant<|im_sep|>
    # - <|im_start|>assistant budget=63909 yield_budget=12800<|im_sep|>
    # - <|im_start|>assistant budget=58259 yield_budget=1280 latency=2524 reasoning_steps=101 tokens=11452<|im_sep|>
    # - and any other text between 'assistant' and <|im_sep|>
    MESSAGE_START_PATTERN: str = r"^<\|im_start\|>assistant(?: [^<]*)?<\|im_sep\|>"

    # MESSAGE_END_PATTERN: str = "<|im_end|>"
    MESSAGE_END_PATTERN: str = "<|fim_suffix|>"

    @property
    def accepts_invalid(self) -> bool:
        """Whether invalid samples should be sent to `grade_batch()`. Since our samples are all
        invalid but we still want to use the grading codepath we need to set this to True for this
        benchmark.
        """
        return True

    def _has_no_model_errors(
        self, sample: types.SampleWithCompletion[datapoint.MathgenDatapoint]
    ) -> bool:
        return len(sample.errors_blamed_on_model) == 0

    def _strip_markdown_code_block(self, text: str) -> str:
        """
        Strips markdown code block tags from a string.
        Handles blocks like ```python\n<code>\n``` or ```\n<code>\n```.
        Returns the code inside, or the original text if no code block is found.
        """
        import re

        # Match ```<lang>?<newline>code<newline>```
        pattern = re.compile(r"^```(?:\w+)?\n(.*?)\n?```$", re.DOTALL)
        match = pattern.match(text.strip())
        if match:
            return match.group(1)
        return text

    def _clean_given_answer(self, given_answer):
        # Support for all MESSAGE_START_PATTERNs:
        # - <|im_start|>assistant<|im_sep|>
        # - <|im_start|>assistant budget=63909 yield_budget=12800<|im_sep|>
        # - <|im_start|>assistant budget=58259 yield_budget=1280 latency=2524 reasoning_steps=101 tokens=11452<|im_sep|>
        # - and any other text between 'assistant' and <|im_sep|>
        MESSAGE_START_PATTERN: str = r"^<\|im_start\|>assistant(?: [^<]*)?<\|im_sep\|>"
        # Support for cases where the message may start with just <|im_sep|>
        ALT_MESSAGE_START_PATTERN: str = r"^<\|im_sep\|>"

        # MESSAGE_END_PATTERN: str = "<|im_end|>"
        # Support both <|fim_suffix|> and <|im_end|> as valid message end patterns
        MESSAGE_END_PATTERNS = [r"<\|fim_suffix\|>", r"<\|im_end\|>"]

        # Compile all end patterns into a single regex
        end_pattern = re.compile("(" + "|".join(MESSAGE_END_PATTERNS) + ")$")

        start_pattern = re.compile(MESSAGE_START_PATTERN)
        alt_start_pattern = re.compile(ALT_MESSAGE_START_PATTERN)
        start_match = start_pattern.match(given_answer)
        alt_start_match = alt_start_pattern.match(given_answer)
        end_match = end_pattern.search(given_answer)

        # Accept both <|im_start|>...<|im_sep|> and <|im_sep|> as valid starts, but also handle when only <|im_sep|> is present at the start
        if ((start_match and end_match) or (alt_start_match and end_match)) and given_answer.count(
            "<|endoftext|>"
        ) == 0:
            if start_match:
                tags_removed = given_answer[start_match.end() : end_match.start()]
            else:
                tags_removed = given_answer[alt_start_match.end() : end_match.start()]
            markdown_removed = self._strip_markdown_code_block(tags_removed)
            return markdown_removed

        if end_match:
            # remove the end pattern from the answer
            end_index = end_match.start()
            given_answer = given_answer[:end_index]

        # If the answer starts with <|im_sep|> and is just a code block, handle that
        alt_start_pattern_only = re.compile(r"^<\|im_sep\|>")
        if alt_start_pattern_only.match(given_answer):
            tags_removed = given_answer[alt_start_pattern_only.match(given_answer).end() :]
            markdown_removed = self._strip_markdown_code_block(tags_removed)
            return markdown_removed

        # If the answer is just a code block, strip markdown
        if given_answer.strip().startswith("```"):
            return self._strip_markdown_code_block(given_answer)

        # Let's assume the answer is a simple string without any special formatting
        return given_answer

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:

        graded_samples = []

        for sample in samples:
            gt_answer = sample.gt_datapoint.answer
            given_answer = sample.text.strip()
            given_answer_cleaned = self._clean_given_answer(given_answer=given_answer)

            # Compare extracted answer with ground truth
            is_correct = (
                given_answer_cleaned is not None
                and given_answer_cleaned == gt_answer
                and self._has_no_model_errors(sample)
            )

            log_reward = 0 if is_correct else -float("inf")

            graded_samples.append(
                sample.with_grade(
                    log_rewards={"answer_matches": log_reward},
                    is_correct=is_correct,
                    given_answer=given_answer_cleaned,
                    telemetry_metadata={"ground_truth_answer": gt_answer},
                )
            )
        return graded_samples


def get_completion(text, token_completer, max_tokens=500):
    convo = chat.Conversation(messages=[chat.Message.user(text)])

    renderer = get_renderer("harmony_v4.0.15_berry_v3_1mil_orion_no_budget")
    stop_sequences = renderer.encoding.encode_batch(
        ["<|fim_suffix|>", "<|im_end|>", "<|endoftext|>"], allowed_special="all"
    )

    tokens = renderer.render_for_completion_multimodal_toklist(convo, role=chat.Role.ASSISTANT)
    # _ensure_llm_grader_is_ready(token_completer)

    results = token_completer.completion(
        [tokens],
        max_tokens=max_tokens,
        temperature=0,
        logprobs=0,  # The number of most probable tokens to return their probabilities for each token position.
        stop=stop_sequences,
        seed=2025,
    )

    outs = renderer.decode(results.choices[0].toklist.spans[0].tokens)
    prefixes = ["<|im_start|>assistant<|im_sep|>", "<|meta_sep|>analysis<|im_sep|>"]
    for prefix in prefixes:
        if outs.startswith(prefix):
            outs = outs[len(prefix) :]
    return outs.strip()


def get_judge_prompt(
    original_code_block: str,
    change_description: str,
    suggested_code_change: str,
    ground_truth_code_block: str,
    model_generated_code_block: str,
) -> str:
    prompt = f"""
You are an expert code judge. Your task is to compare a LLM-generated code block (model_generated_code_block) to a ground_truth_code_block (ground_truth_code_block).

The LLM was given these three things:
1. A code block that represents the code that is currently open in the editor, aka the original_code_block.
2. A change description that describes the change to be applied, aka the change_description.
3. A code block that represents a suggested code change, aka the suggested_code_change. This could be a complete code block or a diff. If it's a diff, the delimiter is `...existing code...`.
The LLM was asked to merge the original_code_block with the suggested_code_change, as guided by the change_description and write the model_generated_code_block.

You have to compare the model_generated_code_block to the ground_truth_code_block and assign a score from 0 to 2 based on how well the generated code block matches the ground_truth_code_block.

# Scoring rules
1. Score as 0 if any of below applies:
    - model_generated_code_block has syntax error.
    - model_generated_code_block has non-trivial logical error in the given context.
    - model_generated_code_block contains offensive content, PII, hate speech etc.
    - model_generated_code_block contains excessive irrelevant code or comments, or repeatitive code or comments.
    - model_generated_code_block leaves out important context from the ground_truth_code_block.
2. Score as 1 if any of below applies:
    - model_generated_code_block has some code style not aligned with the given context.
    - model_generated_code_block has some indentation or space issues but doesn't hurt the syntax correctness.
    - model_generated_code_block has the right code following the merge but introduces a few extra functions or code blocks that's not neccessary.
    - model_generated_code_block is exactly what user conceptually wants. It adds similar implementation as the golden code completion but not necessarily exactly matches.
3. Score as 2 if any of below applies:
    - model_generated_code_block and ground_truth_code_block are functionally equivalent.
    - In case, the given context is too vague to understand user's intent, model_generated_code_block is reasonable and alternative to the golden code completion.

Instructions:
1. Be very critical and conservative of scores. We generally would prefer a lower score.
2. Format your response as following:
    - Reasoning: <your reasoning here on how you arrived at the score>
    - Final Score: <score>

# Inputs

original_code_block:
{original_code_block}

change_description:
{change_description}

suggested_code_change:
{suggested_code_change}

ground_truth_code_block:
{ground_truth_code_block}

model_generated_code_block:
{model_generated_code_block}

# Outputs
Start your answer now, first the reasoning, then the score in a new line with `Final Score:` prefix.
Reasoning:"""
    return prompt.strip()


def extract_judge_score(judge_output: str) -> int:
    """
    Extracts the integer score from the judge's output.
    Handles a variety of natural language and explicit formats, e.g.:
    - So 4
    - I'd give 3
    - Final Score: 3
    - So score 2
    - Score 3
    - I'd say 4
    - Let's choose 3
    - I'll choose 3
    Returns None if no valid score is found.
    """
    import re

    patterns = [
        r"Score:\s*([0-2])\b",
        r"score\s*([0-2])\b",
        r"pick\s*([0-2])\b",
        r"So\s*([0-2])\b",
        r"I'd give\s*([0-2])\b",
        r"I'd say\s*([0-2])\b",
        r"I'll choose\s*([0-2])\b",
        r"choose\s*([0-2])\b",
        r"score likely\s*([0-2])\b",
    ]
    for pat in patterns:
        match = re.search(pat, judge_output, re.IGNORECASE)
        if match:
            score = int(match.group(1))
            logger.info(f"!prd extract_score: Matched pattern '{pat}' with score {score}")
            return score
    logger.info(f"!prd extract_score: No score found in output: {judge_output[:100]}")
    return None


@chz.chz(typecheck=True)
class SolutionComparisonGrader(AnswerCorrectnessGrader):
    topic: str = chz.field(
        doc="The teacher model snapshot, used for routing to the correct bus engine.",
    )

    topic_mode_or_user: str = chz.field(
        doc="The topic mode or user used for creating the engines.",
    )

    line: str = chz.field(
        doc="The bus line to use.",
        default=DEFAULT_BUS_LINE,
    )

    judge_max_tokens: int = chz.field(
        doc="The maximum number of tokens to generate for the judge.",
        default=2048,
    )

    @chz.init_property
    def bus_completer(self) -> BusTokenCompleter:
        if self.topic is not None and self.topic_mode_or_user is not None:
            return BusTokenCompleter(
                topic_or_snapshot=self.topic,
                topic_mode_or_user=self.topic_mode_or_user,
                bus_line=self.line,
            )
        return None

    def get_judge_score(
        self,
        sample,
        original_code_block,
        change_description,
        suggested_code_change,
        ground_truth_code_block,
        model_generated_code_block,
    ):
        prompt = get_judge_prompt(
            original_code_block,
            change_description,
            suggested_code_change,
            ground_truth_code_block,
            model_generated_code_block,
        )
        logger.info(f"!prd Judge prompt: {prompt}")
        judge_result = get_completion(
            prompt, token_completer=self.bus_completer, max_tokens=self.judge_max_tokens
        )
        logger.info(f"!prd Judge result: {judge_result}")
        judge_score = extract_judge_score(judge_result)
        logger.info(f"!prd Judge score: {judge_score}")
        return judge_score, judge_result

    def _grade_solution_task(self, generated_text, gt_answer, sample):
        given_answer_cleaned = self._clean_given_answer(given_answer=generated_text.strip())
        logger.info(f"!prd Grading sample: {generated_text=}, {given_answer_cleaned=}")

        if given_answer_cleaned == gt_answer:
            logger.info(f"!prd Sample is correct: {given_answer_cleaned} == {gt_answer}")
            return (
                given_answer_cleaned,
                True,
                math.log(1),
                "Judge not called, answer and completion are exactly same",
                2,
            )

        is_correct = False
        judge_result = None
        judge_score = None
        reward = -float("inf")

        if given_answer_cleaned is not None:
            instant_apply_fields = sample.gt_datapoint.metadata["instant_apply_fields"]

            judge_score, judge_result = self.get_judge_score(
                sample=sample,
                original_code_block=instant_apply_fields["user_code_block_language"],
                change_description=instant_apply_fields["change_description"],
                suggested_code_change=instant_apply_fields["suggested_code_change"],
                ground_truth_code_block=gt_answer,
                model_generated_code_block=given_answer_cleaned,
            )
            is_correct = judge_score is not None and judge_score >= 1
            reward = math.log(judge_score / 2) if is_correct else -float("inf")
        return (
            given_answer_cleaned,
            is_correct,
            reward,
            judge_result,
            judge_score,
        )

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        graded_samples = []
        for sample in samples:
            gt_answer = sample.gt_datapoint.answer

            (
                given_answer_cleaned,
                is_correct,
                reward,
                judge_result,
                judge_score,
            ) = self._grade_solution_task(sample.text, gt_answer, sample)
            graded_samples.append(
                sample.with_grade(
                    log_rewards={
                        "reward": reward if is_correct else -float("inf"),
                    },
                    is_correct=is_correct,
                    given_answer=sample.text,
                    telemetry_metadata={
                        "ground_truth_answer": str(
                            {
                                "given_answer_cleaned": given_answer_cleaned,
                                "judge_result": judge_result,
                                "judge_score": judge_score,
                            }
                        ),
                    },
                )
            )
        return graded_samples
