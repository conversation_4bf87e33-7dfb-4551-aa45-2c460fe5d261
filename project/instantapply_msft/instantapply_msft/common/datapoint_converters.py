import copy
import logging
import re
import textwrap
import uuid
from typing import Any, Sequence

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def msft_postprocess_harmony_data(dp: dict[str, Any]) -> Sequence[dict[str, Any]]:
    import copy

    res = copy.deepcopy(dp)
    if "answer_only" in res:
        res.pop("answer_only")

    return [res]


def draft_sampling_prompt_1(dp: dict[str, Any]) -> Sequence[dict[str, Any]]:
    import copy

    res = copy.deepcopy(dp)
    if "answer_only" in res:
        res.pop("answer_only")

    system_prompt = """You are an AI programming assistant that is specialized in applying code changes to an existing document.
Follow Microsoft content policies. Avoid content that violates copyrights. Keep your answers short and impersonal.
If you are asked to generate content that is harmful, hateful, racist, sexist, lewd, violent, or completely irrelevant to software engineering, only respond with "Sorry, I can't assist with that."""

    task_description = """An LLM was given a code block in a specific programming language, a change description, and it came up with a suggested code change, so that the code block is modified according to the change description.

You are given all the three things:
1. A code block that represents the code that is currently open in the editor, aka the original_code_block.
2. A change description that describes the change to be applied, aka the change_description.
3. A code block that represents a suggested code change, aka the suggested_code_change. This could be a complete code block or a diff. If it's a diff, the delimiter is `...existing code...`. The delimiter specifies the block(s) of code that the LLM has generated, guided by the change_description.

You have to figure out how to merge the suggested_code_change into the original_code_block, and rewrite the original_code_block to fully incorporate the code changes guided by the change_description. Mainly figure out where the code block(s) from suggested_code_change should be inserted into the original_code_block.

Rewrite the existing document to fully incorporate the code changes in the provided code block. For the response, always follow these instructions:
1. Analyse the original_code_block and the suggested_code_change to decide if the code block should replace existing code or should be merged.
2. If necessary, break up the code blocks in multiple parts and insert each part at the appropriate location.
3. Preserve whitespace and newlines right after the parts of the file that you modify.
4. The final result must be syntactically and semantically valid, properly formatted, and correctly indented. 
5. You must output the fully merged file as your solution."""

    def draft_problem(instant_apply_fields):
        # input file:
        user_code_block_language = instant_apply_fields["user_code_block_language"]
        user_code_block = instant_apply_fields["user_code_block"]

        # description of the change:
        change_description = instant_apply_fields["change_description"]

        # diff
        suggested_code_block_language = instant_apply_fields["suggested_code_block_language"]
        suggested_code_change = instant_apply_fields["suggested_code_change"]

        return f"""{system_prompt}
    {task_description.format(user_code_block_language=user_code_block_language)}
    The original_code_block:
    ```{user_code_block_language}
    {user_code_block}
    ```

    The change_description:
    {change_description}

    The suggested_code_change:
    ```{suggested_code_block_language}
    {suggested_code_change}
    ```"""

    res["problem"] = draft_problem(res["metadata"]["instant_apply_fields"])

    return [res]
