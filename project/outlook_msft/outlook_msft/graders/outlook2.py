import json
import math
import random
import re
from collections import defaultdict
from typing import Sequence

import chat
import chz
import structlog
from bus.registry import DEFAULT_BUS_LINE
from bus_token_completer.bus_token_completer import BusTokenCompleter
from chat.render import get_renderer
from multimodal_token.toklist.list import TokList
from qstar.common import datapoint, types
from qstar.graders import grader
from qstar.sample_completers import sample_completer

logger = structlog.stdlib.get_logger(component=__name__)


def get_completion(text, token_completer, max_tokens=500):
    convo = chat.Conversation(messages=[chat.Message.user(text)])

    renderer = get_renderer("harmony_v4.0.15_16k_orion_text_only_no_asr_2k_action")
    stop_sequences = renderer.encoding.encode_batch(
        ["<|fim_suffix|>", "<|im_end|>", "<|endoftext|>"], allowed_special="all"
    )

    tokens = renderer.render_for_completion_multimodal_toklist(convo, role=chat.Role.ASSISTANT)
    # _ensure_llm_grader_is_ready(token_completer)

    results = token_completer.completion(
        [tokens],
        max_tokens=max_tokens,
        temperature=0,
        logprobs=0,  # The number of most probable tokens to return their probabilities for each token position.
        stop=stop_sequences,
        seed=2025,
    )

    outs = renderer.decode(results.choices[0].toklist.spans[0].tokens)
    prefixes = ["<|im_start|>assistant<|im_sep|>", "<|meta_sep|>analysis<|im_sep|>"]
    for prefix in prefixes:
        if outs.startswith(prefix):
            outs = outs[len(prefix) :]
    return outs.strip()


reasoning_test_prompt_by_outlook = """<|im_start|>system
You are an advanced multi-lingual AI assistant. You can understand and communicate fluently in the user's language of choice. Your task is to process multiple pseudo-code functions that describe a scoring criteria to evaluate an email subject line and a reasoning text about that email's importance and content. You need to reason using that subject line and reasoning text to return the answers. You will receive a Scoring Criteria and must judge the user input based on them.

# Scoring criteria: You will score the quality of the reasoning text in a scale from 0 to 5. Start by assuming the score is 5 and then subtract points from it based on the following code

def find_substring_in_text(text, substrings):
    if find_patterns(substrings, text):
        return True
    else:
        return False

def has_idle_recommendations(reasoning_text, idle_actions):
    if(similar_match(reasoning_text, idle_actions)):
        return True
    else:
        return False

def has_reasoning(reasoning_text):
    if not(empty(reasoning_text)) or reasoning_text != "No reason provided":
         return True
    else:
        return False

def has_headline_tags(reasoning_text):
    headline_tags = ["<headline>", "</headline>"]
    if headline_tags in reasoning_text:
        return True
    else:
        return False

def is_subject_similar_to_headline(subject_line, headline_text):
    if similar(headline_text, subject_line):
        return True
    else:
        return False

def refers_to_user_by_you(reasoning_text):
    if user_referred_to_by_you:
        return True
   elif user_referred_to_by_actual_name:
       return False
  return False

def user_title_mentioned(reasoning_text):
    if any(title) in reasoning_text:
        return True
    else:
        return False

def is_headline_good(headline_text):
    if(punchy(headline_text) and concise(headline_text) and clear(headline_text)):
        return True
    else:
        return False

def is_reasoning_good(reasoning_text):
    if(succinct(reasoning_text) and coherent(reasoning_text) and not_repetitive(reasoning_text) and not_verbose(reasoning_text)):
        return True
    else:
        return False

<|im_end|> 
<|im_start|>user
subject_line =  "{Subject}"
reasoning_text = "{PrioritizeReason}"
<|im_end|> 
<|im_start|>system
# Context
substrings = ["This **email**", "This **e-mail**", "The **email**", "The **e-mail**"]
idle_actions = ["no immediate action required", "doesn't need your input/attention", "no action needed", "doesn't require immediate"]
{{'User inputs are in {{Language}}. If they are not in English, then translate each item in substrings and idle_actions to match the user input language.' if self.multilanguage else 'NO MULTILANGUAGE OPERATOR' }} 

# Output
Take a deep breath and work step-by-step.
0) What are the substrings and idle_actions?
1) What is the output of has_reasoning? If False, score should be set to 0 immediately.
2) What is the output of has_headline? If False, score should be set to 0 immediately.
3) Break the reasoning text into smaller sections and check each section for the presence of the substrings defined in step 0 using find_substring_in_reasoning_text? If the function returns True for **any** section, subtract 1 point.
4) What is the output of has_idle_recommendations considering the idle_actions defined in the **Context** section? If True, subtract 1 point.
5) What is the output of is_subject_similar_to_headline? If True, subtract 1 point.
6) What is the output of is_headline_good? If False, subtract 1 point.
7) What is the output of is_reasoning_good? If False, subtract 1 point.
8) What is the output of refers_to_user_by_you? If False, subtract 1 point.
9) What is the output of user_title_mentioned? If True, subtract 1 point.
10) Given that the initial score is always 5 and cannot be lower than 0, how many points were deducted from the steps above? What is the final score?
11) Return the final score from step 10 wrapped in <score> and </score> tags. Also add a justification in full sentences on why you scored it that way, and wrap it with <reason> and </reason> tags.
<|im_end|> 
<|im_start|>assistant
0)"""


def get_prompt(problem: str, generation: str, ground_truth: str) -> str:
    """
    Returns a prompt for the judge LLM to compare a generated reasoning to the ground truth.
    The judge must present its reasoning step-by-step before giving the score, and return a JSON object.
    """
    prompt = f"""
You are an expert evaluator. Your task is to compare a generated reasoning text to a ground truth reasoning text.

# Instructions

Step-by-step, do the following:
1. Analyze both the ground truth and generated reasoning for:
   - Reference to the user's role and responsibilities.
   - Mention of relevant project, task, or topic from the email.
   - Correct identification of urgency and required action (or lack thereof).
   - Mention of the sender's relationship to the user (manager, collaborator, etc.) if relevant.
   - Factual accuracy (no hallucinated or fabricated details).
   - Conciseness, clarity, and non-repetitiveness.
   - Whether the score is justified based on the above.
2. Present your reasoning and comparison in detail.
3. Assign an integer score from 0, 1 or 2. Lower scores mean the generated text is worse than ground truth.
4. Deduct points for missing references, inaccuracies, hallucinations, or irrelevant information. 
5. What should NOT be mentioned in the reasoning text (give a score of 0 if any of these are mentioned):
    - Do not use 3rd person, mention the user by name or by "the user". Instead, use second person language like "you" or "your". Act like you are talking to the user directly.
    - Do not mention the score explicitly in the reasoning text.
    - Do not mention the reasoning process or steps taken to arrive at the score.
    - That we specify a score to the reasoning text. For example, do not say "score 2" or "Therefore, it is assigned score..."
    - No fluff text.
    - More than 2 sentences. Sentences should be grammatically correct and meaningful. If not, give a score of 0 at once.
    - Long & verbose sentences are not accepted. 
    - Do not have "this email" mentioned in the reasoning text.
6. What is desirable in the reasoning text (give a score of 0 if these points are not satisfied):
    - The reasoning text should be concise, clear, and non-repetitive.
    - We prefer shorter, concise reasoning texts that still cover all the important points. Not verbose or overly detailed.
    - The reasoning text should be coherent and logically structured.
    - Short and meaningful sentences are preferred.
7. Rubric to follow when scoring:
    - Score 0: The generated text is even slightly worse than the ground truth. For eg, missing manager mention (if required) or missing references that ground truth covers.
    - Score 1: The generated text and ground truth are very much comparable in quality, with generated text being slightly better or at least on par with ground truth. Nothing is missing.
    - Score 2: The generated text is better than the ground truth.
8. Be very critical and conservative of scores. We generally would prefer a lower score.
9. Format your response as following:
    - Reasoning: <your reasoning here on how you arrived at the score>
    - Final Score: <score>

# Inputs

Problem:
{problem}

Ground Truth Reasoning:
{ground_truth}

Generated Reasoning:
{generation}

Start your answer now, first the reasoning, then the score in a new line with `Final Score:` prefix.

Reasoning:"""
    return prompt.strip()


def extract_score(judge_output: str) -> int:
    """
    Extracts the integer score from the judge's output.
    Handles a variety of natural language and explicit formats, e.g.:
    - So 4
    - I'd give 3
    - Final Score: 3
    - So score 2
    - Score 3
    - I'd say 4
    - Let's choose 3
    - I'll choose 3
    Returns None if no valid score is found.
    """
    import re

    patterns = [
        r"Score:\s*([1-5])\b",
        r"score\s*([1-5])\b",
        r"pick\s*([1-5])\b",
        r"So\s*([1-5])\b",
        r"I'd give\s*([1-5])\b",
        r"I'd say\s*([1-5])\b",
        r"I'll choose\s*([1-5])\b",
        r"choose\s*([1-5])\b",
        r"score likely\s*([1-5])\b",
    ]
    for pat in patterns:
        match = re.search(pat, judge_output, re.IGNORECASE)
        if match:
            score = int(match.group(1))
            logger.info(f"!prd extract_score: Matched pattern '{pat}' with score {score}")
            return score
    logger.info(f"!prd extract_score: No score found in output: {judge_output[:100]}")
    return None


@chz.chz(typecheck=True)
class AnswerCorrectnessGrader(grader.Grader[datapoint.MathgenDatapoint]):
    """
    Grader that extracts a score from the completion, compares it to the ground truth answer.
    The sample is correct only if both the score matches. Rejects the sample if no score is found.

    Expects data in Harmony data format:
    sample.gt_datapoint.problem = chat_format_dict

    The response follows this exact JSON format:
    {
        "Score": <integer_between_1_and_5>,
        "Reason": "<concise_reasoning_within_quotes>"
    }

    `sample.gt_datapoint.metadata["score"]` contains the ground truth score and `sample.gt_datapoint.metadata["reason"]` contains the ground truth reason.
    This grader checks if the score matches the ground truth score.
    """

    stop_sequences: tuple = chz.field(
        doc="The bus line to use.",
        default=("<|fim_suffix|>", "<|im_end|>", "<|endoftext|>"),
    )

    @property
    def accepts_invalid(self) -> bool:
        """Whether invalid samples should be sent to `grade_batch()`. Since our samples are all
        invalid but we still want to use the grading codepath we need to set this to True for this
        benchmark.
        """
        return True

    def _has_no_model_errors(
        self, sample: types.SampleWithCompletion[datapoint.MathgenDatapoint]
    ) -> bool:
        return len(sample.errors_blamed_on_model) == 0

    def remove_stop_sequences(self, text: str) -> str:
        """
        Removes the stop sequences from the text.
        This is useful to ensure that the text does not contain any stop sequences that might interfere with JSON parsing.
        """
        prefix = "<|im_start|>assistant<|im_sep|>"
        if text.startswith(prefix):
            text = text[len(prefix) :]

        for seq in self.stop_sequences:
            text = text.replace(seq, "")
        return text.strip()

    def extract_score_and_reason(self, text: str):
        """
        Extracts the score and reason from the text.
        Expects the text to be in the format:
        {
            "Score": <integer_between_1_and_5>,
            "Reason": "<concise_reasoning_within_quotes>"
        }
        """
        try:
            response = json.loads(self.remove_stop_sequences(text))
            score = response["Score"]
            reason = response["Reason"]
            return response, score, reason
        except:
            return None, None, None

    def _grade_solution_task(self, generated_text, gt_score, gt_reason, sample):
        response, score, reason = self.extract_score_and_reason(generated_text)
        # logger.info(
        #     f"!prd Grading sample: gt_score={gt_score}, gt_reasoning_trace={gt_reason}, response={response}, score={score}, reason={reason}"
        # )
        score_is_correct = (
            score is not None and score == gt_score and self._has_no_model_errors(sample)
        )
        reason_is_correct = False
        judge_result = None
        judge_score = None

        sample.metrics["score_reward"] = 1 if score_is_correct else 0
        return (response, score_is_correct, reason_is_correct, judge_result, reason, score)

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        graded_samples = []
        for sample in samples:
            gt_score = sample.gt_datapoint.metadata["score"]
            gt_reason = sample.gt_datapoint.metadata["reason"]
            (
                response,
                score_is_correct,
                reason_is_correct,
                judge_result,
                reason,
                score,
            ) = self._grade_solution_task(sample.text, gt_score, gt_reason, sample)
            graded_samples.append(
                sample.with_grade(
                    log_rewards={
                        "score_reward": 0 if score_is_correct else -float("inf"),
                    },
                    is_correct=score_is_correct,
                    given_answer=sample.text,
                    telemetry_metadata={
                        "ground_truth_answer": str(
                            {
                                "score": gt_score,
                                "reason": gt_reason,
                                "response": response,
                            }
                        ),
                    },
                )
            )
        return graded_samples


@chz.chz(typecheck=True)
class SolutionComparisonGrader(AnswerCorrectnessGrader):
    topic: str = chz.field(
        doc="The teacher model snapshot, used for routing to the correct bus engine.",
    )

    topic_mode_or_user: str = chz.field(
        doc="The topic mode or user used for creating the engines.",
    )

    line: str = chz.field(
        doc="The bus line to use.",
        default=DEFAULT_BUS_LINE,
    )

    @chz.init_property
    def bus_completer(self) -> BusTokenCompleter:
        if self.topic is not None and self.topic_mode_or_user is not None:
            return BusTokenCompleter(
                topic_or_snapshot=self.topic,
                topic_mode_or_user=self.topic_mode_or_user,
                bus_line=self.line,
            )
        return None

    def get_judge_score(self, sample, reason, gt_reasoning_trace):
        prompt = get_prompt(sample.gt_datapoint.problem, reason, gt_reasoning_trace)
        logger.info(f"!prd Judge prompt: {prompt}")
        judge_result = get_completion(prompt, token_completer=self.bus_completer)
        logger.info(f"!prd Judge result: {judge_result}")
        judge_score = extract_score(judge_result)
        logger.info(f"!prd Judge score: {judge_score}")
        return judge_score, judge_result

    def _grade_solution_task(self, generated_text, gt_score, gt_reason, sample):
        response, score, reason = self.extract_score_and_reason(generated_text)
        logger.info(
            f"!prd Grading sample: gt_score={gt_score}, gt_reasoning_trace={gt_reason}, response={response}, score={score}, reason={reason}"
        )
        score_is_correct = (
            score is not None and score == gt_score and self._has_no_model_errors(sample)
        )
        reason_is_correct = False
        judge_result = None
        judge_score = None
        if score_is_correct and reason is not None:
            judge_score, judge_result = self.get_judge_score(sample, reason, gt_reason)
            reason_is_correct = judge_score is not None and judge_score >= 1
        sample.metrics["score_reward"] = 1 if score_is_correct else 0
        sample.metrics["reason_reward"] = 1 if reason_is_correct else 0
        return (response, score_is_correct, reason_is_correct, judge_result, reason, score)

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:
        graded_samples = []
        for sample in samples:
            gt_score = sample.gt_datapoint.metadata["score"]
            gt_reason = sample.gt_datapoint.metadata["reason"]
            (
                response,
                score_is_correct,
                reason_is_correct,
                judge_result,
                reason,
                score,
            ) = self._grade_solution_task(sample.text, gt_score, gt_reason, sample)
            graded_samples.append(
                sample.with_grade(
                    log_rewards={
                        "score_reward": 0 if score_is_correct else -float("inf"),
                        "reason_reward": 0 if reason_is_correct else -float("inf"),
                    },
                    is_correct=score_is_correct and reason_is_correct,
                    given_answer=sample.text,
                    telemetry_metadata={
                        "ground_truth_answer": str(
                            {
                                "score": gt_score,
                                "reason": gt_reason,
                                "response": response,
                                "judge_result": judge_result,
                            }
                        ),
                    },
                )
            )
        return graded_samples


class SolutionReferencePoolManager:
    def __init__(self):
        self._pools = defaultdict(list)
        self._unique_completions = set()
        self._golden_ref_count = 0
        self._ref_sample_count = 0

    def get(self, dp_id, golden):
        pool = self._pools[dp_id]
        if not pool:
            pool.append((golden, float("inf")))
        return pool

    def update(self, dp_id, candidate, judge_score, pool_size, prompt, ref, judge_fn):
        if judge_score is None or judge_score <= 1:
            return
        pool = self._pools[dp_id]
        if any(candidate == c for c, _ in pool):
            return
        self._unique_completions.add(candidate)
        should_add = True
        if judge_fn is not None and prompt is not None and ref is not None:
            should_add = judge_fn(prompt, candidate, ref)
        if not should_add:
            return
        pool.append((candidate, judge_score))
        golden_tuple = pool[0]
        rest = sorted(pool[1:], key=lambda x: x[1], reverse=True)[:pool_size]
        self._pools[dp_id] = [golden_tuple] + rest

    def log_reference_sampling(self, ref, golden):
        self._ref_sample_count += 1
        if ref == golden:
            self._golden_ref_count += 1

    @property
    def unique_completions_count(self):
        return len(self._unique_completions)

    @property
    def golden_reference_frequency(self):
        if self._ref_sample_count == 0:
            return 0.0
        return self._golden_ref_count / self._ref_sample_count

    def get_pool_avg_score(self, dp_id):
        pool = self._pools[dp_id]
        if not pool:
            return 0.0
        # Exclude golden (index 0, score inf) from average
        scores = [score for _, score in pool[1:] if score != float("inf")]
        if not scores:
            return 0.0
        return sum(scores) / len(scores)


SOLUTION_REFERENCE_POOL_MANAGER = SolutionReferencePoolManager()


@chz.chz(typecheck=True)
class SolutionComparisonDynamicRefPoolGrader(SolutionComparisonGrader):
    POOL_SIZE: int = 7

    def _get_datapoint_id(self, sample):
        return hash(sample.gt_datapoint.problem)

    def _get_reference_pool(self, sample, golden):
        dp_id = self._get_datapoint_id(sample)
        return SOLUTION_REFERENCE_POOL_MANAGER.get(dp_id, golden)

    def _choose_reference(self, pool, golden=None):
        ref_tuple = random.choice(pool)
        ref = ref_tuple[0]
        if golden is not None:
            SOLUTION_REFERENCE_POOL_MANAGER.log_reference_sampling(ref, golden)
        return ref

    def _update_reference_pool(
        self, sample, candidate, judge_score, prompt=None, ref=None, judge_fn=None
    ):
        dp_id = self._get_datapoint_id(sample)
        SOLUTION_REFERENCE_POOL_MANAGER.update(
            dp_id, candidate, judge_score, self.POOL_SIZE, prompt, ref, judge_fn
        )

    @property
    def unique_completions_count(self):
        return SOLUTION_REFERENCE_POOL_MANAGER.unique_completions_count

    @property
    def golden_reference_frequency(self):
        return SOLUTION_REFERENCE_POOL_MANAGER.golden_reference_frequency

    def get_pool_avg_score(self, sample):
        dp_id = self._get_datapoint_id(sample)
        return SOLUTION_REFERENCE_POOL_MANAGER.get_pool_avg_score(dp_id)

    def _grade_solution_task(self, generated_text, gt_score, gt_reason, sample):
        response, score, reason = self.extract_score_and_reason(generated_text)
        score_is_correct = (
            score is not None and score == gt_score and self._has_no_model_errors(sample)
        )
        reason_is_correct = False
        judge_result = None
        judge_score = None
        pool = None
        if score_is_correct and reason is not None:
            golden = gt_reason
            pool = self._get_reference_pool(sample, golden)
            ref = self._choose_reference(pool, golden=golden)
            judge_score, judge_result = self.get_judge_score(sample, reason, ref)
            logger.info(f"!prd Judge result: {judge_result}")
            logger.info(f"!prd Judge score: {judge_score}")
            self._update_reference_pool(
                sample, reason, judge_score, prompt=sample.gt_datapoint.problem, ref=ref
            )
            reason_is_correct = judge_score is not None and judge_score >= 1
            logger.info(
                f"!prd Pool size: {len(pool)}, Unique completions: {self.unique_completions_count}, Golden freq: {self.golden_reference_frequency}, Pool avg score: {self.get_pool_avg_score(sample)}"
            )
        sample.metrics["reference_pool_size"] = len(pool) if pool else 0
        sample.metrics["unique_completions_in_pool"] = self.unique_completions_count
        sample.metrics["golden_reference_frequency"] = self.golden_reference_frequency
        sample.metrics["reference_pool_avg_score"] = self.get_pool_avg_score(sample)
        sample.metrics["score_reward"] = 1 if score_is_correct else 0
        sample.metrics["reason_reward"] = 1 if reason_is_correct else 0
        return (response, score_is_correct, reason_is_correct, judge_result, reason, score)
