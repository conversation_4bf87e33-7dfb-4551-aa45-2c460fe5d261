import re
from typing import Sequence

import chz
from multimodal_token.toklist.list import Tok<PERSON>ist
from qstar.common import datapoint, types
from qstar.graders import grader
from qstar.sample_completers import sample_completer


@chz.chz(typecheck=True)
class AnswerCorrectnessGrader(grader.Grader[datapoint.MathgenDatapoint]):
    """
    Grader that extracts a score from the completion, compares it to the ground truth answer.
    The sample is correct only if both the score matches. Rejects the sample if no score is found.

    Expects data in PSA data format:
    # Problem\n\n" + text + "\n# Solution:\n\n" + reasoning_trace + "\n# Answer\n\n" + answer
    """

    @property
    def accepts_invalid(self) -> bool:
        """Whether invalid samples should be sent to `grade_batch()`. Since our samples are all
        invalid but we still want to use the grading codepath we need to set this to True for this
        benchmark.
        """
        return True

    def _has_no_model_errors(
        self, sample: types.SampleWithCompletion[datapoint.MathgenDatapoint]
    ) -> bool:
        return len(sample.errors_blamed_on_model) == 0

    def _extract_answer(self, text: str) -> int | None:
        """
        Extracts a single digit answer from the text following the pattern "# Answer\\n\\n".
        This could be in between a lot of text.
        Returns the answer as an integer or None if no valid single digit is found.
        """
        match = re.search(r"# Answer\s*\n\n.*?(\d)", text, re.DOTALL)
        if match:
            return int(match.group(1))
        return None

    def _grade_batch_inner(
        self,
        samples: Sequence[types.SampleWithCompletion[datapoint.MathgenDatapoint]],
        sample_execution_context: sample_completer.SampleExecutionContext | None = None,
        profile_label: str | None = None,
    ) -> list[types.SampleWithGrade[datapoint.MathgenDatapoint]]:

        graded_samples = []

        for sample in samples:
            sample_score = self._extract_answer(sample.text)
            gt_score = int(sample.gt_datapoint.answer)

            # Compare extracted answer with ground truth
            is_correct = (
                sample_score is not None
                and sample_score == gt_score
                and self._has_no_model_errors(sample)
            )

            log_reward = 0 if is_correct else -float("inf")
            given_answer = str(sample_score) if is_correct else None

            additional_metrics = {
                "answer_matches": is_correct,
            }

            graded_samples.append(
                sample.with_grade(
                    log_rewards={"answer_matches": log_reward},
                    is_correct=is_correct,
                    given_answer=given_answer,
                    telemetry_metadata={"ground_truth_answer": gt_score},
                )
            )
        return graded_samples
