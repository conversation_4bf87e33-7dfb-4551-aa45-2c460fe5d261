{"cells": [{"cell_type": "code", "execution_count": 1, "id": "89232e4d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["loading ./outlook_data/synthetic_email_and_thread_2025-04-28.json\n", "10157\n"]}], "source": ["import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import json\n", "import glob\n", "\n", "def read_json(file_path):\n", "    all_data = []\n", "    for fname in glob.glob(file_path):\n", "        print(f\"loading {fname}\")\n", "        with open(fname, 'r') as file:\n", "            all_data.extend(json.loads(file.read()))\n", "    return all_data\n", "\n", "json_data = read_json(file_path=\"./outlook_data/synthetic_email_and_thread_2025-04-28.json\")\n", "print(len(json_data))"]}, {"cell_type": "code", "execution_count": 2, "id": "55ef716a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 21, 22}\n"]}], "source": ["def count_emails_per_element(data):\n", "    return [len(element['emails']) for element in data]\n", "\n", "email_counts = set(count_emails_per_element(json_data))\n", "print(email_counts)"]}, {"cell_type": "code", "execution_count": 3, "id": "a2e054cc", "metadata": {}, "outputs": [{"data": {"text/plain": ["(501, 10157)"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["def count_names_per_element(data):\n", "    return set([element[\"metadata\"][\"user_info\"][\"name\"] for element in data])\n", "\n", "names = count_names_per_element(json_data)\n", "len(names), len(json_data)"]}, {"cell_type": "code", "execution_count": 4, "id": "f24631e3", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>metadata</th>\n", "      <th>emails</th>\n", "      <th>IsThread</th>\n", "      <th>Score</th>\n", "      <th>Reason</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>{'user_info': {'name': '<PERSON><PERSON>', 'ema...</td>\n", "      <td>[{'From': '<PERSON><PERSON><PERSON>@fujitsunetworks.com',...</td>\n", "      <td>False</td>\n", "      <td>4</td>\n", "      <td>This email is important because it involves cl...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>{'user_info': {'name': '<PERSON><PERSON>', 'email'...</td>\n", "      <td>[{'From': '<EMAIL>', 'T...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>This email is a newsletter providing updates o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>{'user_info': {'name': '<PERSON>', 'email'...</td>\n", "      <td>[{'From': '<EMAIL>', 'To': [...</td>\n", "      <td>False</td>\n", "      <td>1</td>\n", "      <td>This email is from a promotional sender, offer...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>{'user_info': {'name': '<PERSON>', 'email'...</td>\n", "      <td>[{'From': '<EMAIL>', ...</td>\n", "      <td>False</td>\n", "      <td>2</td>\n", "      <td>This email is a newsletter from 'Engineering W...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>{'user_info': {'name': '<PERSON>', 'email'...</td>\n", "      <td>[{'From': 'linda.martine<PERSON>@mohawkglobal.com', '...</td>\n", "      <td>False</td>\n", "      <td>4</td>\n", "      <td>This email is important because it involves th...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10152</th>\n", "      <td>{'user_info': {'name': '<PERSON>', 'email'...</td>\n", "      <td>[{'From': '<PERSON><PERSON>@howardcompany.com', 'T...</td>\n", "      <td>True</td>\n", "      <td>3</td>\n", "      <td>This thread is of moderate importance as it pe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10153</th>\n", "      <td>{'user_info': {'name': '<PERSON>', 'em...</td>\n", "      <td>[{'From': '<PERSON><PERSON><EMAIL>', 'To': ['Mi...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>The thread is somewhat important as it provide...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10154</th>\n", "      <td>{'user_info': {'name': '<PERSON>', 'email':...</td>\n", "      <td>[{'From': '<EMAIL>', 'To': ...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>This thread is somewhat important as it provid...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10155</th>\n", "      <td>{'user_info': {'name': '<PERSON>', 'email':...</td>\n", "      <td>[{'From': '<PERSON><PERSON>@ttlusa.com', 'To': ...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>The thread is somewhat important as it involve...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10156</th>\n", "      <td>{'user_info': {'name': '<PERSON><PERSON>', 'email...</td>\n", "      <td>[{'From': '<EMAIL>', 'To'...</td>\n", "      <td>True</td>\n", "      <td>2</td>\n", "      <td>This thread is somewhat important as it involv...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>10157 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                                                metadata  \\\n", "0      {'user_info': {'name': '<PERSON><PERSON>', 'ema...   \n", "1      {'user_info': {'name': '<PERSON><PERSON>', 'email'...   \n", "2      {'user_info': {'name': '<PERSON>', 'email'...   \n", "3      {'user_info': {'name': '<PERSON>', 'email'...   \n", "4      {'user_info': {'name': '<PERSON>', 'email'...   \n", "...                                                  ...   \n", "10152  {'user_info': {'name': '<PERSON>', 'email'...   \n", "10153  {'user_info': {'name': '<PERSON>', 'em...   \n", "10154  {'user_info': {'name': '<PERSON>', 'email':...   \n", "10155  {'user_info': {'name': '<PERSON>', 'email':...   \n", "10156  {'user_info': {'name': '<PERSON><PERSON>', 'email...   \n", "\n", "                                                  emails  IsThread  Score  \\\n", "0      [{'From': '<PERSON><PERSON><PERSON>@fujitsunetworks.com',...     False      4   \n", "1      [{'From': '<EMAIL>', 'T...     False      2   \n", "2      [{'From': '<EMAIL>', 'To': [...     False      1   \n", "3      [{'From': '<EMAIL>', ...     False      2   \n", "4      [{'From': '<EMAIL>', '...     False      4   \n", "...                                                  ...       ...    ...   \n", "10152  [{'From': '<PERSON><PERSON>@howardcompany.com', 'T...      True      3   \n", "10153  [{'From': '<PERSON><PERSON><EMAIL>', 'To': ['Mi...      True      2   \n", "10154  [{'From': '<EMAIL>', 'To': ...      True      2   \n", "10155  [{'From': '<PERSON><PERSON>@ttlusa.com', 'To': ...      True      2   \n", "10156  [{'From': '<EMAIL>', 'To'...      True      2   \n", "\n", "                                                  Reason  \n", "0      This email is important because it involves cl...  \n", "1      This email is a newsletter providing updates o...  \n", "2      This email is from a promotional sender, offer...  \n", "3      This email is a newsletter from 'Engineering W...  \n", "4      This email is important because it involves th...  \n", "...                                                  ...  \n", "10152  This thread is of moderate importance as it pe...  \n", "10153  The thread is somewhat important as it provide...  \n", "10154  This thread is somewhat important as it provid...  \n", "10155  The thread is somewhat important as it involve...  \n", "10156  This thread is somewhat important as it involv...  \n", "\n", "[10157 rows x 5 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = pd.DataFrame(json_data)\n", "df"]}, {"cell_type": "code", "execution_count": 5, "id": "23763c29", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Plot the frequency of email counts\n", "df['emails'].apply(len).value_counts().sort_index().plot(kind='bar')\n", "plt.xlabel('Number of Emails in Thread')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Email Counts per Row')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 6, "id": "64bd227e", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def plot_is_thread(df, is_new_plot=True):\n", "    \"\"\"\n", "    Plots the distribution of the 'IsThread' column in the dataframe.\n", "    \"\"\"\n", "    df['IsThread'].value_counts().plot(kind='bar')\n", "    if is_new_plot:\n", "        plt.xlabel('IsThread')\n", "        plt.ylabel('Count')\n", "        plt.title('Distribution of IsThread in DataFrame')\n", "        plt.xticks([0, 1], ['False', 'True'], rotation=0)\n", "        plt.show()\n", "    \n", "\n", "plot_is_thread(df)"]}, {"cell_type": "code", "execution_count": 7, "id": "487ef10c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Train size: 9099, Test size: 1058\n"]}], "source": ["from sklearn.model_selection import GroupKFold\n", "\n", "def group_kfold_split(json_data, n_splits=5, random_state=2025):\n", "    # Extract groups by user name\n", "    groups = [element[\"metadata\"][\"user_info\"][\"name\"] for element in json_data]\n", "    gkf = GroupKFold(n_splits=n_splits, shuffle=True, random_state=random_state)\n", "    splits = []\n", "    for train_idx, test_idx in gkf.split(json_data, groups=groups):\n", "        train = [json_data[i] for i in train_idx]\n", "        test = [json_data[i] for i in test_idx]\n", "        splits.append((train, test))\n", "    return splits\n", "\n", "splits = group_kfold_split(json_data, n_splits=10)\n", "train_data, test_data = splits[0]\n", "print(f\"Train size: {len(train_data)}, Test size: {len(test_data)}\")"]}, {"cell_type": "code", "execution_count": 8, "id": "a8e6de5e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fold 0: No group overlap between train and test.\n", "Fold 1: No group overlap between train and test.\n", "Fold 2: No group overlap between train and test.\n", "Fold 3: No group overlap between train and test.\n", "Fold 4: No group overlap between train and test.\n", "Fold 5: No group overlap between train and test.\n", "Fold 6: No group overlap between train and test.\n", "Fold 7: No group overlap between train and test.\n", "Fold 8: No group overlap between train and test.\n", "Fold 9: No group overlap between train and test.\n", "All data points are covered in the splits.\n"]}], "source": ["# Verify that group_kfold_split produces non-overlapping groups and covers all data\n", "def verify_group_kfold_split(json_data, splits):\n", "    all_indices = set()\n", "    for i, (train, test) in enumerate(splits):\n", "        train_names = set(element[\"metadata\"][\"user_info\"][\"name\"] for element in train)\n", "        test_names = set(element[\"metadata\"][\"user_info\"][\"name\"] for element in test)\n", "        # Check that train and test groups do not overlap\n", "        overlap = train_names & test_names\n", "        if overlap:\n", "            print(f\"Fold {i}: Overlapping groups found: {overlap}\")\n", "        else:\n", "            print(f\"Fold {i}: No group overlap between train and test.\")\n", "        # Collect all indices for coverage check\n", "        all_indices.update(id(e) for e in train)\n", "        all_indices.update(id(e) for e in test)\n", "        \n", "    # Check if all data points are covered\n", "    if len(all_indices) == len(json_data):\n", "        print(\"All data points are covered in the splits.\")\n", "    else:\n", "        print(f\"Data coverage issue: {len(json_data) - len(all_indices)} data points missing.\")\n", "\n", "verify_group_kfold_split(json_data, splits)"]}, {"cell_type": "code", "execution_count": 9, "id": "c7ef2b54", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "def plot_email_scores(json_data):\n", "    scores = []\n", "    for element in json_data:\n", "        scores.append(element['Score'])\n", "    \n", "    # Plot the scores\n", "    plt.hist(scores, bins=range(1, 7), align='left', rwidth=0.8)\n", "\n", "plt.xlabel('Scores')\n", "plt.ylabel('Frequency')\n", "plt.title('Distribution of Email Scores')\n", "plt.xticks(range(1, 6))\n", "\n", "plot_email_scores(train_data)\n", "plot_email_scores(test_data)"]}, {"cell_type": "code", "execution_count": 10, "id": "61f7f211", "metadata": {}, "outputs": [], "source": ["# plot_is_thread(pd.DataFrame(train_data), is_new_plot=True)\n", "# plot_is_thread(pd.DataFrame(test_data), is_new_plot=True)"]}, {"cell_type": "code", "execution_count": 11, "id": "95c57210", "metadata": {}, "outputs": [], "source": ["# pd.DataFrame(train_data)['emails'].apply(len).value_counts().sort_index().plot(kind='bar')\n", "# plt.xlabel('Number of Emails in Thread')\n", "# plt.ylabel('Frequency')\n", "# plt.title('Distribution of Email Counts per Row')\n", "# plt.show()\n", "\n", "# pd.DataFrame(test_data)['emails'].apply(len).value_counts().sort_index().plot(kind='bar')\n", "# plt.xlabel('Number of Emails in Thread')\n", "# plt.ylabel('Frequency')\n", "# plt.title('Distribution of Email Counts per Row')\n", "# plt.show()"]}, {"cell_type": "code", "execution_count": 12, "id": "ed9318bf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["User: <PERSON><PERSON> (R&D Research Intern)\n", "Email: <PERSON><PERSON><PERSON>@coopervision.com\n", "Manager: <PERSON> (<EMAIL>)\n", "Skip Manager: <PERSON> (<EMAIL>)\n", "Top Collaborators:\n", "  - <PERSON> (<EMAIL>)\n", "  - <PERSON> (<EMAIL>)\n", "  - <PERSON> (<EMAIL>)\n", "\n", "Work Responsibility: <PERSON><PERSON> is responsible for conducting research on camera lenses, focusing on areas like image stabilization, autofocus, and lens specifications, to contribute to product development and technical documentation.\n", "\n", "Projects:\n", "  - Contact Lens Efficacy Project [Ongoing]: <PERSON><PERSON> is conducting a comprehensive literature review and data analysis on contact lens efficacy, with a focus on astigmatism solutions, and is preparing a report for senior management.\n", "  - Astigmatism Solutions Project [In Progress]: <PERSON><PERSON> has completed the literature review and is now working on data analysis, with a draft report expected by next week.\n", "\n", "Top Emails:\n", "  - [2023-09-20T15:30:00Z] Request for Additional Resources for Literature Search (<PERSON>): <PERSON> requested additional resources for <PERSON><PERSON>'s literature search on contact lens efficacy studies.\n", "  - [2023-09-17T09:00:00Z] Progress Update on Contact Lens Efficacy Studies (<PERSON>): <PERSON> provided a progress update on the contact lens efficacy studies <PERSON><PERSON> is working on, highlighting her dedication and collaboration with the team.\n", "\n", "Top Meetings:\n", "  - [2020-11-23T14:00:00] Review of Astigmatism Solutions in Contact Lenses (Organizer: <PERSON>): A meeting to review the findings and progress on astigmatism solutions in contact lenses, involving <PERSON><PERSON>, <PERSON>, and <PERSON>.\n", "  - [2020-12-15T11:00:00] Consumer Acceptance of Plant-Based Meats (Organizer: <PERSON>): A discussion on consumer acceptance of plant-based meats attended by industry leaders.\n"]}], "source": ["def pretty_print_metadata(metadata, indent=0, handled_keys=None):\n", "    if handled_keys is None:\n", "        handled_keys = set()\n", "    user = metadata.get('user_info', {})\n", "    projects = metadata.get('projects', [])\n", "    top_emails = metadata.get('top_emails', [])\n", "    top_meetings = metadata.get('top_meetings', [])\n", "    lines = []\n", "    # User Info\n", "    if user:\n", "        lines.append(f\"User: {user.get('name', '')} ({user.get('job_title', '')})\")\n", "        lines.append(f\"Email: {user.get('email', '')}\")\n", "        if 'manager' in user:\n", "            lines.append(f\"Manager: {user['manager'].get('name', '')} ({user['manager'].get('email', '')})\")\n", "        if 'skip_manager' in user:\n", "            lines.append(f\"Skip Manager: {user['skip_manager'].get('name', '')} ({user['skip_manager'].get('email', '')})\")\n", "        if 'top_collaborators' in user:\n", "            lines.append(\"Top Collaborators:\")\n", "            for c in user['top_collaborators']:\n", "                lines.append(f\"  - {c.get('name', '')} ({c.get('email', '')})\")\n", "        handled_keys.add('user_info')\n", "    # Work Responsibility\n", "    if 'work_responsibility' in metadata:\n", "        lines.append(f\"\\nWork Responsibility: {metadata['work_responsibility']}\")\n", "        handled_keys.add('work_responsibility')\n", "    # Projects\n", "    if projects:\n", "        lines.append(\"\\nProjects:\")\n", "        for p in projects:\n", "            lines.append(f\"  - {p.get('name', '')} [{p.get('status', '')}]: {p.get('details', '')}\")\n", "        handled_keys.add('projects')\n", "    # Top Emails\n", "    if top_emails:\n", "        lines.append(\"\\nTop Emails:\")\n", "        for e in top_emails:\n", "            lines.append(f\"  - [{e.get('dateTime', '')}] {e.get('subject', '')} ({e.get('from', '')}): {e.get('summary', '')}\")\n", "        handled_keys.add('top_emails')\n", "    # Top Meetings\n", "    if top_meetings:\n", "        lines.append(\"\\nTop Meetings:\")\n", "        for m in top_meetings:\n", "            lines.append(f\"  - [{m.get('dateTime', '')}] {m.get('subject', '')} (Organizer: {m.get('organizer', '')}): {m.get('summary', '')}\")\n", "        handled_keys.add('top_meetings')\n", "    # Print any unknown fields generically\n", "    def print_unknown(obj, indent=0):\n", "        pad = '  ' * indent\n", "        if isinstance(obj, dict):\n", "            for k, v in obj.items():\n", "                if k in handled_keys:\n", "                    continue\n", "                if isinstance(v, (dict, list)):\n", "                    lines.append(f\"{pad}{k}:\")\n", "                    print_unknown(v, indent+1)\n", "                else:\n", "                    lines.append(f\"{pad}{k}: {v}\")\n", "        elif isinstance(obj, list):\n", "            for i, item in enumerate(obj):\n", "                lines.append(f\"{pad}-\")\n", "                print_unknown(item, indent+1)\n", "    print_unknown(metadata, indent)\n", "    return '\\n'.join(lines)\n", "\n", "print(pretty_print_metadata(json_data[-1]['metadata']))"]}, {"cell_type": "code", "execution_count": 13, "id": "4ecf238f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Email #1\n", "From: <EMAIL>\n", "To: <PERSON><PERSON><PERSON>@coopervision.com, <EMAIL>\n", "Subject: Potential Resources for Literature Review\n", "Body: <PERSON> and <PERSON>,\n", "\n", "I hope this message finds you well. I came across a few articles and studies that might be useful for our literature review on contact lens efficacy. Some of these resources include recent findings on lens material advancements and their impact on astigmatism correction.\n", "\n", "Please let me know if you find them relevant, or if there are specific areas you’d like me to focus the search on. Looking forward to your thoughts.\n", "\n", "Best,\n", "<PERSON>\n", "\n", "Email #2\n", "From: <PERSON><PERSON><PERSON>@coopervision.com\n", "To: <EMAIL>, <EMAIL>\n", "Subject: Re: Potential Resources for Literature Review\n", "Body: <PERSON>,\n", "\n", "Thank you for sharing these resources. I briefly reviewed the articles, and they seem quite relevant to our current focus on astigmatism solutions. I believe the section on lens material advancements could provide valuable insights for the ongoing data analysis.\n", "\n", "<PERSON>, let me know if you have any additional thoughts or if there's anything specific you’d like to explore further.\n", "\n", "Thanks again, <PERSON>, for your help!\n", "\n", "Best regards,\n", "<PERSON><PERSON>\n"]}], "source": ["def flatten_email_thread(emails):\n", "    \"\"\"\n", "    Flattens a list of email dicts into a neat string representation,\n", "    separating each email with a divider.\n", "    \"\"\"\n", "\n", "    def _flatten(email):\n", "        parts = []\n", "        for key in ['From', 'To', 'Subject', 'Body']:\n", "            if key in email:\n", "                value = email[key]\n", "                if isinstance(value, list):\n", "                    value = ', '.join(value)\n", "                parts.append(f'{key}: {value}')\n", "        return '\\n'.join(parts)\n", "\n", "    if len(emails) == 1:\n", "        return _flatten(emails[0])\n", "\n", "    flattened = []\n", "    for idx, email in enumerate(emails):\n", "        flattened.append(f\"Email #{idx+1}\\n\" + _flatten(email))\n", "    return '\\n\\n'.join(flattened)\n", "\n", "print(flatten_email_thread(json_data[-1]['emails']))"]}, {"cell_type": "code", "execution_count": 14, "id": "4c4c77b1", "metadata": {}, "outputs": [], "source": ["old_prompt = \"\"\"You are an intelligent assistant for {username}, tasked with evaluating the importance of incoming emails based on their metadata. Your role is to provide a priority score and a detailed explanation for your evaluation.\n", "\n", "# Instructions\n", "- Score: Assign a single integer score from 1 to 5, where 1 represents low importance and 5 represents high importance.\n", "- Reason: Provide a clear and concise paragraph explaining why you assigned this score. Your explanation should reference the email's content, context, or any relevant metadata provided, such as topics or relationships mentioned in {username}'s preferences.\n", "\n", "# Rewrite step\n", "Follow these guidelines when writing the text:\n", "- Clear and concise communication here is key here. Therefore, DO NOT USE any passings like \"This/the email thread\", \"This/the email...\", or something similar. The user already knows this is an email thread. Redact and rephrase accordingly.\n", "- DO NOT state the obvious, like who the email is from. \n", "- If there is no immediate action required from {username}, then DO NOT say things like \"no immediate action required from you\" or \"does not require your input\"; just refrain from mentioning that altogether.\n", "- Write a final sentence as if you were briefing {username} on HOW the email affects them as a {user_title}. Should {username} pay attention?\n", "- Write the text as if you were talking directly to {username}. In other words, use \"you\" instead of \"{username}\".\n", "- DO NOT say anything about who reports to whom or who manages whom.\n", "- DO NOT say anything about what {username} does or does not work on.\n", "- DO NOT include job titles or chains of command of any kind in the text.\n", "- The rewrite must not explicitly cite the score in its text.\n", "- *Never* generate biased, racist, sexist, gender biased or otherwise inappropriate content.\n", "- *Never* provide content related to suicide, killing or other physical violence.\n", "- **Always** use the pronoun 'they' instead of 'he' or 'she'.\n", "- **Always** use the pronoun 'them' instead of 'him' or 'her'.\n", "- **Always** use the pronoun 'their' instead of 'his' or 'hers'.\n", "\n", "# Metadata\n", "{flattened_metadata}\n", "\n", "# Emails\n", "{flattened_emails}\"\"\""]}, {"cell_type": "code", "execution_count": 15, "id": "2f3c5cbb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ./prompt_v0.yaml\n"]}], "source": ["%%writefile ./prompt_v0.yaml\n", "\n", "system_prompt:\n", "  - \"You are an intelligent assistant for {username}, tasked with evaluating the importance of incoming emails based on their metadata.\"\n", "  - \"Your role is to provide a priority score and a detailed explanation for your evaluation.\"\n", "\n", "instructions:\n", "  - \"Instructions\"\n", "  - \"Score: Assign a single integer score from 1 to 5, where 1 represents low importance and 5 represents high importance.\"\n", "  - \"Reason: Provide a clear and concise paragraph explaining why you assigned this score. Your explanation should reference the email's content, context, or any relevant metadata provided, such as topics or relationships mentioned in {username}'s preferences.\"\n", "\n", "rewrite_step:\n", "  - \"Follow these guidelines when writing the text:\"\n", "  - \"Clear and concise communication here is key here. Therefore, DO NOT USE any passings like \\\"This/the email thread\\\", \\\"This/the email...\\\", or something similar. The user already knows this is an email thread. Redact and rephrase accordingly.\"\n", "  - \"DO NOT state the obvious, like who the email is from.\"\n", "  - \"If there is no immediate action required from {username}, then DO NOT say things like \\\"no immediate action required from you\\\" or \\\"does not require your input\\\"; just refrain from mentioning that altogether.\"\n", "  - \"Write a final sentence as if you were briefing {username} on HOW the email affects them as a {user_title}. Should {username} pay attention?\"\n", "  - \"Write the text as if you were talking directly to {username}. In other words, use 'you' instead of {username}.\"\n", "  - \"DO NOT say anything about who reports to whom or who manages whom.\"\n", "  - \"DO NOT say anything about what {username} does or does not work on.\"\n", "  - \"DO NOT include job titles or chains of command of any kind in the text.\"\n", "  - \"The rewrite must not explicitly cite the score in its text.\"\n", "  - \"*Never* generate biased, racist, sexist, gender biased or otherwise inappropriate content.\"\n", "  - \"*Never* provide content related to suicide, killing or other physical violence.\"\n", "  - \"**Always** use the pronoun 'they' instead of 'he' or 'she'.\"\n", "  - \"**Always** use the pronoun 'them' instead of 'him' or 'her'.\"\n", "  - \"**Always** use the pronoun 'their' instead of 'his' or 'hers'.\"\n", "\n", "metadata:\n", "  - \"{flattened_metadata}\"\n", "\n", "emails:\n", "  - \"{flattened_emails}\""]}, {"cell_type": "code", "execution_count": 16, "id": "51f9a863", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ./inference_prompt_v4.yaml\n"]}], "source": ["%%writefile ./inference_prompt_v4.yaml\n", "\n", "system_prompt:\n", "  - \"You are an intelligent assistant for {username}, tasked with evaluating the importance of incoming emails based on their metadata and content.\"\n", "  - \"Your job is to assign a realistic priority score and a concise, actionable explanation.\"\n", "  - \"Format the response as Score: <score>\\\\nReason: <reasoning>.\"\n", "\n", "ambiguity_handling:\n", "  - \"**Ambiguity Handling**\"\n", "  - \"If you are unsure between two scores, choose the lower score unless there is clear, explicit evidence supporting the higher score.\"\n", "  - \"When the message is ambiguous or does not clearly fit any category, err on the side of caution and assign the less important score.\"\n", "  - \"If the message is equally balanced between two categories, prefer the lower score.\"\n", "  - \"If you cannot find explicit evidence for urgency, relevance, or required action, do not assume it—assign the lowest plausible score.\"\n", "\n", "scoring_rubric:\n", "  - \"**Scoring Rubric**\"\n", "  - \"**1:** Generic, promotional, or irrelevant. No connection to {username}'s work or projects. Can be skipped.\"\n", "  - \"**2:** Routine, administrative, or background project communication. Includes scheduling, confirming attendance, sharing non-critical updates, or coordinating standard tasks. No decisions, substantive input, or changes to {username}'s work are required. These can be safely deprioritized.\"\n", "  - \"**3:** Project communications that require {username}'s awareness because they may affect {username}'s work, involve a decision, or request substantive input. Not urgent, but more than just routine coordination.\"\n", "  - \"**4:** Important for {username}'s work. Timely attention is needed, but not an emergency. Only assign if the message contains **explicit evidence** of time sensitivity (such as a stated deadline, urgent request, or clear risk if not addressed soon).\"\n", "\n", "boundary_table:\n", "  - \"**Boundary Table**\"\n", "  - \"| Situation                                      | Score | Why?                                               |\"\n", "  - \"|------------------------------------------------|-------|----------------------------------------------------|\"\n", "  - \"| Project update, no input needed                | 2     | Routine, can be read at convenience                |\"\n", "  - \"| Project update, asks for awareness/input       | 3     | Awareness/input needed, not urgent                 |\"\n", "  - \"| Manager requests action, deadline, critical    | 5     | Immediate, critical, from authority                |\"\n", "  - \"| Manager requests action, deadline, not critical| 4     | Timely, but not critical                           |\"\n", "\n", "forced_choice_reminders:\n", "  - \"**General Rules & Forced Choice Reminders**\"\n", "  - \"Do NOT assign a score of 3 just because a message is related to {username}'s project. Only assign 3 if {username}'s awareness is needed for a possible decision, change, or substantive input. Otherwise, assign 2.\"\n", "  - \"Do NOT assign a score of 5 unless the message states that failure to act will cause immediate, major negative consequences.\"\n", "  - \"Do NOT assign a score of 4 or 5 unless the message contains explicit evidence of urgency, such as a stated deadline, urgent request, or clear risk if not addressed.\"\n", "  - \"Do NOT infer urgency from context alone (such as \\\"upcoming events\\\" or \\\"requested input\\\"). Only explicit deadlines or urgent language qualify.\"\n", "  - \"Requests for input, coordination, or sharing insights without a deadline or explicit urgency should be scored 3, even if they are relevant.\"\n", "  - \"Most newsletters, updates, or general information should be scored 1 or 2, even if they are related to {username}'s field.\"\n", "  - \"Only assign 3 if the content is relevant and useful, but not urgent or requiring immediate action.\"\n", "\n", "distinguishing_2_vs_3:\n", "  - \"**Distinguishing 2 vs 3**\"\n", "  - \"**Checklist for 3:**\"\n", "  - \"Assign 3 if:\"\n", "  - \"  - The message asks for your awareness, feedback, or input,\"\n", "  - \"  - OR it contains information that could affect your work,\"\n", "  - \"  - EVEN IF it is not urgent.\"\n", "  - \"**Positive Example for 3:**\"\n", "  - \"A project update that asks for your feedback or awareness of a change, even without a deadline, should be 3.\"\n", "  - \"**Negative Example for 2:**\"\n", "  - \"A project update that does not ask for input or awareness, and is just for your information, should be 2.\"\n", "  - \"See General Rules & Forced Choice Reminders.\"\n", "\n", "distinguishing_4_vs_5:\n", "  - \"**Distinguishing 4 vs 5**\"\n", "  - \"**Checklist for 5:**\"\n", "  - \"Assign 5 ONLY if:\"\n", "  - \"  - The message is from your manager or a key stakeholder,\"\n", "  - \"  - AND it requires immediate action,\"\n", "  - \"  - AND it states a critical deadline or consequence for not acting.\"\n", "  - \"**Example for 5:**\"\n", "  - \"If your manager says, 'I need your feedback by EOD today or we will miss the client deadline,' assign 5.\"\n", "  - \"**Example for 4:**\"\n", "  - \"If your manager says, 'Please review this by tomorrow so we stay on track,' assign 4.\"\n", "  - \"See General Rules & Forced Choice Reminders.\"\n", "\n", "special_note_deadlines_meetings:\n", "  - \"**Special Note on Deadlines and Meetings**\"\n", "  - \"Do NOT treat routine project deadlines, scheduled meetings, or standard planning milestones as urgent or critical. These are a normal part of work and should not be rated higher than 2 or 3 unless there is explicit evidence that missing the deadline would cause significant negative consequences.\"\n", "  - \"Only rate as 4 or 5 if the message contains an unexpected, last-minute, or high-impact deadline, or if it describes a situation where immediate action is required to prevent a problem.\"\n", "  - \"Routine coordination, planning, or preparation—even if a date is mentioned—should be rated 2 (mildly relevant) or 3 (relevant but not urgent), unless there is clear evidence of urgency or risk.\"\n", "  - \"If the message is only about scheduling, confirming, or routine preparation for a meeting or presentation, and does not require you to make a decision, provide substantive input, or change {username}'s work, assign a score of 2.\"\n", "  - \"See General Rules & Forced Choice Reminders.\"\n", "\n", "explanation_guidelines:\n", "  - \"**Explanation Guidelines**\"\n", "  - \"If you assign a score of 4 or 5, you must quote the exact words or phrases from the email that prove urgency or a deadline.\"\n", "  - \"If you cannot find explicit evidence of urgency or a deadline, do not assign a score higher than 3.\"\n", "  - \"Write a single, clear, and concise paragraph.\"\n", "  - \"DO NOT use phrases like \\\"This email...\\\", \\\"This thread...\\\", or similar.\"\n", "  - \"DO NOT state the obvious, such as who the email is from.\"\n", "  - \"DO NOT mention job titles, reporting lines, or management structure.\"\n", "  - \"DO NOT mention what {username} does or does not work on.\"\n", "  - \"DO NOT cite the score in your explanation.\"\n", "  - \"Use \\\"you\\\" to address {username} directly.\"\n", "  - \"Always use 'they', 'them', and 'their' as pronouns.\"\n", "  - \"If no action is needed, do not mention that fact.\"\n", "  - \"End with a sentence briefing {username} on how the message affects them in their role, and whether they should pay close attention, review at their convenience, or safely skip it.\"\n", "\n", "positive_example:\n", "  - \"**Positive Example**\"\n", "  - \"If a message says, \\\"Let's finalize the presentation by next Thursday,\\\" but this is part of regular project planning, rate it as 2 or 3, not 4 or 5. Only rate higher if the message says something like, \\\"We must finalize this today or we will miss a critical opportunity,\\\" or \\\"Immediate action is required to resolve an urgent issue.\\\"\"\n", "\n", "negative_examples:\n", "  - \"**Negative Examples**\"\n", "  - \"A thread about preparing slides, confirming a meeting time, or sharing drafts for routine review—without requiring decisions or changes—should be rated 2, not 3.\"\n", "  - \"Do NOT say: \\\"You should pay attention because it is from your manager.\\\" (Sender alone does not determine importance.)\"\n", "  - \"Do NOT say: \\\"This is a newsletter about your industry, so it is important.\\\" (Newsletters are rarely urgent or critical.)\"\n", "  - \"Do NOT say: \\\"No immediate action is required from you.\\\" (Just omit this.)\"\n", "  - \"Do NOT assign a high score just because the topic is relevant or you are asked for input; urgency and direct impact are required for a higher score.\"\n", "\n", "metadata:\n", "  - \"{flattened_metadata}\"\n", "\n", "emails:\n", "  - \"{flattened_emails}\""]}, {"cell_type": "code", "execution_count": 17, "id": "a1ab444e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Overwriting ./training_prompt_v4.yaml\n"]}], "source": ["%%writefile ./training_prompt_v4.yaml\n", "\n", "system_prompt:\n", "  - \"You are an intelligent assistant for {username}, tasked with evaluating the importance of incoming emails based on their metadata and content.\"\n", "  - \"Assign a priority score (1-5, where 1 represents low importance and 5 represents high importance) and a concise, actionable explanation.\"\n", "  - \"Respond in the format: \\\"# Solution\\n<the explanation>\\\\n# Answer\\\\n\\\\n<the score>.\"\n", "  - \"Your explanation should reference the email's content, context, or any relevant metadata provided, such as topics or relationships mentioned in {username}'s preferences.\"\n", "\n", "scoring_rubric:\n", "  - \"Scoring Rubric:\"\n", "  - \"1: Generic, promotional, or irrelevant. Assign only if the message is pure spam, a generic promotion, or completely unrelated to {username}'s field, company, or projects. Examples: \\\"Grow your own tomatoes! Gardening tips newsletter.\\\"; \\\"Exclusive offer: Save 20% on office supplies.\\\"\"\n", "  - \"2: Routine, administrative, or background communication. Related to your field, company, or projects, but does not require awareness, input, or action. Examples: \\\"Monthly Tech News: October Edition.\\\"; \\\"Company-wide update: New HR policies.\\\"\"\n", "  - \"3: Requires your awareness, feedback, or input (not urgent). Assign if the message asks for your awareness, feedback, or input, or contains information that could affect your work, even if not urgent. Example: \\\"Please review the attached and let me know if you have any questions. Let's sync next week.\\\"\"\n", "  - \"4: Important and time-sensitive (not critical). Assign if the message is important for your work and timely attention is needed, with a clear deadline or urgent request, but missing it would not cause immediate, major negative impact. Example: \\\"Please review by EOD tomorrow so we can stay on schedule.\\\"\"\n", "  - \"5: Urgent and critical. Assign only if the message is from a manager or key stakeholder, requires immediate action, and states a critical deadline or consequence for not acting. Example: \\\"I need your feedback by EOD today or we will miss the client deadline.\\\"\"\n", "\n", "key_boundaries:\n", "  - \"Key Boundaries:\"\n", "  - \"If unsure between two scores, choose the lower unless there is clear, explicit evidence for the higher.\"\n", "  - \"Do NOT assign 3 just because a message is related to your project—only if your awareness or input is needed.\"\n", "  - \"Do NOT assign 4 or 5 unless the message contains explicit evidence of urgency, such as a stated deadline or urgent request.\"\n", "  - \"Routine meetings, scheduling, or standard updates should be 2 or 3 unless there is clear urgency or risk.\"\n", "  - \"If you assign 4 or 5, quote the exact words or phrases that prove urgency or a deadline.\"\n", "\n", "rewrite_guidelines:\n", "  - \"Rewrite Guidelines:\"\n", "  - \"Clear and concise communication is key. DO NOT use phrases like \\\"This/the email thread\\\", \\\"This/the email...\\\", or similar. The user already knows this is an email thread.\"\n", "  - \"DO NOT state the obvious, like who the email is from.\"\n", "  - \"If there is no immediate action required from {username}, DO NOT say things like \\\"no immediate action required from you\\\" or \\\"does not require your input\\\"; just omit that.\"\n", "  - \"Write a final sentence as if you were briefing {username} on HOW the email affects them as a {user_title}. Should {username} pay attention?\"\n", "  - \"Write as if talking directly to {username}. Use \\\"you\\\" instead of \\\"{username}\\\".\"\n", "  - \"DO NOT mention reporting lines, management structure, or job titles.\"\n", "  - \"DO NOT say what {username} does or does not work on.\"\n", "  - \"DO NOT include job titles or chains of command.\"\n", "  - \"Never generate biased, racist, sexist, gender biased, or otherwise inappropriate content.\"\n", "  - \"Never provide content related to suicide, killing, or other physical violence.\"\n", "  - \"Always use the pronoun 'they' instead of 'he' or 'she'.\"\n", "  - \"Always use the pronoun 'them' instead of 'him' or 'her'.\"\n", "  - \"Always use the pronoun 'their' instead of 'his' or 'hers'.\"\n", "  - \"Do not explicitly cite the score in the explanation.\"\n", "\n", "metadata:\n", "  - \"{flattened_metadata}\"\n", "\n", "emails:\n", "  - \"{flattened_emails}\""]}, {"cell_type": "code", "execution_count": 18, "id": "86ec1f01", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an intelligent assistant for {username}, tasked with evaluating the importance of incoming emails based on their metadata and content.\n", "Assign a priority score (1-5, where 1 represents low importance and 5 represents high importance) and a concise, actionable explanation.\n", "Respond in the format: \"# Solution\n", "<the explanation>\\n# Answer\\n\\n<the score>.\n", "Your explanation should reference the email's content, context, or any relevant metadata provided, such as topics or relationships mentioned in {username}'s preferences.\n", "\n", "Scoring Rubric:\n", "1: Generic, promotional, or irrelevant. Assign only if the message is pure spam, a generic promotion, or completely unrelated to {username}'s field, company, or projects. Examples: \"Grow your own tomatoes! Gardening tips newsletter.\"; \"Exclusive offer: Save 20% on office supplies.\"\n", "2: Routine, administrative, or background communication. Related to your field, company, or projects, but does not require awareness, input, or action. Examples: \"Monthly Tech News: October Edition.\"; \"Company-wide update: New HR policies.\"\n", "3: Requires your awareness, feedback, or input (not urgent). Assign if the message asks for your awareness, feedback, or input, or contains information that could affect your work, even if not urgent. Example: \"Please review the attached and let me know if you have any questions. Let's sync next week.\"\n", "4: Important and time-sensitive (not critical). Assign if the message is important for your work and timely attention is needed, with a clear deadline or urgent request, but missing it would not cause immediate, major negative impact. Example: \"Please review by EOD tomorrow so we can stay on schedule.\"\n", "5: Urgent and critical. Assign only if the message is from a manager or key stakeholder, requires immediate action, and states a critical deadline or consequence for not acting. Example: \"I need your feedback by EOD today or we will miss the client deadline.\"\n", "\n", "Key Boundaries:\n", "If unsure between two scores, choose the lower unless there is clear, explicit evidence for the higher.\n", "Do NOT assign 3 just because a message is related to your project—only if your awareness or input is needed.\n", "Do NOT assign 4 or 5 unless the message contains explicit evidence of urgency, such as a stated deadline or urgent request.\n", "Routine meetings, scheduling, or standard updates should be 2 or 3 unless there is clear urgency or risk.\n", "If you assign 4 or 5, quote the exact words or phrases that prove urgency or a deadline.\n", "\n", "Rewrite Guidelines:\n", "Clear and concise communication is key. DO NOT use phrases like \"This/the email thread\", \"This/the email...\", or similar. The user already knows this is an email thread.\n", "DO NOT state the obvious, like who the email is from.\n", "If there is no immediate action required from {username}, DO NOT say things like \"no immediate action required from you\" or \"does not require your input\"; just omit that.\n", "Write a final sentence as if you were briefing {username} on HOW the email affects them as a {user_title}. Should {username} pay attention?\n", "Write as if talking directly to {username}. Use \"you\" instead of \"{username}\".\n", "DO NOT mention reporting lines, management structure, or job titles.\n", "DO NOT say what {username} does or does not work on.\n", "DO NOT include job titles or chains of command.\n", "Never generate biased, racist, sexist, gender biased, or otherwise inappropriate content.\n", "Never provide content related to suicide, killing, or other physical violence.\n", "Always use the pronoun 'they' instead of 'he' or 'she'.\n", "Always use the pronoun 'them' instead of 'him' or 'her'.\n", "Always use the pronoun 'their' instead of 'his' or 'hers'.\n", "Do not explicitly cite the score in the explanation.\n", "\n", "{flattened_metadata}\n", "\n", "{flattened_emails}\n"]}], "source": ["import yaml\n", "from copy import deepcopy\n", "from itertools import chain\n", "\n", "def load_prompt_sections(yaml_path):\n", "    with open(yaml_path, 'r') as f:\n", "        prompt_sections = yaml.safe_load(f)\n", "    return prompt_sections\n", "\n", "def compose_prompt(sections):\n", "    prompt = []\n", "    for key, value in sections.items():\n", "        if isinstance(value, list):\n", "            prompt.append(\"\\n\".join(value))\n", "        else:\n", "            prompt.append(value)\n", "    return \"\\n\\n\".join(prompt)\n", "\n", "prompt = compose_prompt(load_prompt_sections('training_prompt_v4.yaml'))\n", "print(prompt)"]}, {"cell_type": "code", "execution_count": 19, "id": "1bcaecd7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an intelligent assistant for <PERSON>, tasked with evaluating the importance of incoming emails based on their metadata and content.\n", "Assign a priority score (1-5, where 1 represents low importance and 5 represents high importance) and a concise, actionable explanation.\n", "Respond in the format: \"# Solution\n", "<the explanation>\\n# Answer\\n\\n<the score>.\n", "Your explanation should reference the email's content, context, or any relevant metadata provided, such as topics or relationships mentioned in <PERSON>'s preferences.\n", "\n", "Scoring Rubric:\n", "1: Generic, promotional, or irrelevant. Assign only if the message is pure spam, a generic promotion, or completely unrelated to <PERSON>'s field, company, or projects. Examples: \"Grow your own tomatoes! Gardening tips newsletter.\"; \"Exclusive offer: Save 20% on office supplies.\"\n", "2: Routine, administrative, or background communication. Related to your field, company, or projects, but does not require awareness, input, or action. Examples: \"Monthly Tech News: October Edition.\"; \"Company-wide update: New HR policies.\"\n", "3: Requires your awareness, feedback, or input (not urgent). Assign if the message asks for your awareness, feedback, or input, or contains information that could affect your work, even if not urgent. Example: \"Please review the attached and let me know if you have any questions. Let's sync next week.\"\n", "4: Important and time-sensitive (not critical). Assign if the message is important for your work and timely attention is needed, with a clear deadline or urgent request, but missing it would not cause immediate, major negative impact. Example: \"Please review by EOD tomorrow so we can stay on schedule.\"\n", "5: Urgent and critical. Assign only if the message is from a manager or key stakeholder, requires immediate action, and states a critical deadline or consequence for not acting. Example: \"I need your feedback by EOD today or we will miss the client deadline.\"\n", "\n", "Key Boundaries:\n", "If unsure between two scores, choose the lower unless there is clear, explicit evidence for the higher.\n", "Do NOT assign 3 just because a message is related to your project—only if your awareness or input is needed.\n", "Do NOT assign 4 or 5 unless the message contains explicit evidence of urgency, such as a stated deadline or urgent request.\n", "Routine meetings, scheduling, or standard updates should be 2 or 3 unless there is clear urgency or risk.\n", "If you assign 4 or 5, quote the exact words or phrases that prove urgency or a deadline.\n", "\n", "Rewrite Guidelines:\n", "Clear and concise communication is key. DO NOT use phrases like \"This/the email thread\", \"This/the email...\", or similar. The user already knows this is an email thread.\n", "DO NOT state the obvious, like who the email is from.\n", "If there is no immediate action required from <PERSON>, DO NOT say things like \"no immediate action required from you\" or \"does not require your input\"; just omit that.\n", "Write a final sentence as if you were briefing <PERSON> on HOW the email affects them as a CERTIFIED PUBLIC ACCOUNTANT. Should <PERSON> pay attention?\n", "Write as if talking directly to <PERSON>. Use \"you\" instead of \"<PERSON>\".\n", "DO NOT mention reporting lines, management structure, or job titles.\n", "DO NOT say what <PERSON> does or does not work on.\n", "DO NOT include job titles or chains of command.\n", "Never generate biased, racist, sexist, gender biased, or otherwise inappropriate content.\n", "Never provide content related to suicide, killing, or other physical violence.\n", "Always use the pronoun 'they' instead of 'he' or 'she'.\n", "Always use the pronoun 'them' instead of 'him' or 'her'.\n", "Always use the pronoun 'their' instead of 'his' or 'hers'.\n", "Do not explicitly cite the score in the explanation.\n", "\n", "User: <PERSON> (CERTIFIED PUBLIC ACCOUNTANT)\n", "Email: <EMAIL>\n", "Manager: <PERSON> (<EMAIL>)\n", "Skip Manager: <PERSON> (<EMAIL>)\n", "Top Collaborators:\n", "  - <PERSON> (<EMAIL>)\n", "  - <PERSON> (<EMAIL>)\n", "  - <PERSON> (<EMAIL>)\n", "  - <PERSON> (<EMAIL>)\n", "  - <PERSON> (<EMAIL>)\n", "  - <PERSON> (<EMAIL>)\n", "\n", "Work Responsibility: <PERSON> is responsible for managing financial audits, ensuring compliance with tax guidelines, and improving financial strategies.\n", "\n", "Projects:\n", "  - Security Patch Deployment [Completed]: Urgent deployment of security patches across all systems to protect against vulnerabilities.\n", "  - New Tax Compliance Guidelines [In Progress]: Reviewing and adhering to new tax compliance guidelines issued recently.\n", "  - Financial Strategy Implementation [In Progress]: Implementing a new financial strategy to optimize operations.\n", "  - Data Management Best Practices [In Progress]: Discussing and implementing best practices for data management to improve efficiency.\n", "\n", "Top Emails:\n", "  - [2023-10-10T13:00:00] Upcoming Financial Audit and Compliance Deadlines (<PERSON>): Reminder about upcoming financial audit and compliance deadlines with a detailed checklist attached.\n", "  - [2023-10-05T15:00:00] Strategic Planning Meeting Follow-Up (<PERSON>): Follow-up on strategic planning meeting discussing fiscal optimization and financial discrepancies.\n", "  - [2023-10-20T16:00:00] Strategies for Effective Financial Reporting (<PERSON>): Discussion on strategies to ensure accuracy in financial reporting and optimize processes.\n", "\n", "Top Meetings:\n", "  - [2021-10-31T11:00:00] Impact of AI on Data Storage (Organizer: <PERSON>): Discussing the impact of AI on data storage solutions.\n", "  - [2021-10-29T13:00:00] Data Storage Solutions and Vendor Selection (Organizer: <PERSON>): Meeting to discuss data storage solutions and select appropriate vendors.\n", "  - [2021-11-01T10:00:00] Optimizing Data Storage for Efficiency (Organizer: Jose <PERSON>): Discussion on optimizing data storage for efficiency and cost-effectiveness.\n", "\n", "Email #1\n", "From: <EMAIL>\n", "To: <EMAIL>, <EMAIL>, <EMAIL>\n", "Subject: Follow-Up: New Tax Compliance Guidelines\n", "Body: <PERSON> and Team,\n", "\n", "I wanted to follow up on our recent discussion regarding the new tax compliance guidelines. As you know, these regulations will have a significant impact on our reporting processes, and we need to ensure that all compliance measures are in place by the end of the month.\n", "\n", "<PERSON>, could you please update us on the progress with the implementation? <PERSON> and <PERSON>, your insights on any potential challenges would be invaluable.\n", "\n", "Looking forward to your updates.\n", "\n", "Best,\n", "<PERSON>\n", "\n", "Email #2\n", "From: <EMAIL>\n", "To: maria.gonza<PERSON><PERSON>@urglobal.com, <EMAIL>, <EMAIL>\n", "Subject: Re: Follow-Up: New Tax Compliance Guidelines\n", "Body: <PERSON>,\n", "\n", "Thank you for the reminder. We are currently in the process of reviewing the guidelines and have made some progress in aligning our systems to these new standards. There are a few areas where we might face challenges, particularly in adjusting our current financial reporting practices.\n", "\n", "<PERSON> and <PERSON>, I would appreciate your input on the integration aspects from the IT side to ensure we meet the deadline without any hitches.\n", "\n", "I will ensure that our team provides a detailed update in our next meeting.\n", "\n", "Best,\n", "<PERSON>\n"]}], "source": ["def get_problem(data, prompt=prompt):\n", "    \"\"\"\n", "    Generates a problem statement for the given data using the provided prompt.\n", "    \"\"\"\n", "    metadata = data['metadata']\n", "    emails = data['emails']\n", "    user_title = metadata['user_info']['job_title']\n", "    username = metadata['user_info']['name']\n", "    metadata_str = pretty_print_metadata(metadata)\n", "    emails_str = flatten_email_thread(emails)\n", "    prompt = prompt.format(username=username, user_title=user_title, flattened_metadata=metadata_str, flattened_emails=emails_str)\n", "    return prompt\n", "\n", "print(get_problem(json_data[-10]))"]}, {"cell_type": "code", "execution_count": 20, "id": "e2d2f191", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "---\n", "\n", "The thread is of moderate importance as it involves updates on the new tax compliance guidelines, which are relevant to Ricardo's role as a Certified Public Accountant. Awareness and coordination are necessary, but the matter is not urgent. The discussion includes key stakeholders who are collaborating to ensure smooth implementation.\n", "# Answer\n", "\n", "3\n"]}], "source": ["# def get_answer(data):\n", "#     return f\"Score: {data['Score']}\\nReason: {data['Reason']}\"\n", "\n", "def get_solution(data):\n", "    return f\"\\n{data['Reason']}\\n# Answer\\n\\n{data['Score']}\"\n", "\n", "def get_answer(data):\n", "    return data['Score']\n", "\n", "print(get_answer(json_data[-10]))\n", "print(\"---\")\n", "print(get_solution(json_data[-10]))"]}, {"cell_type": "code", "execution_count": 21, "id": "45114e60", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are an intelligent assistant for <PERSON>, tasked with evaluating the importance of incoming emails based on their metadata and content.\n", "Assign a priority score (1-5, where 1 represents low importance and 5 represents high importance) and a concise, actionable explanation.\n", "Respond in the format: \"# Solution\n", "<the explanation>\\n# Answer\\n\\n<the score>.\n", "Your explanation should reference the email's content, context, or any relevant metadata provided, such as topics or relationships mentioned in <PERSON>'s preferences.\n", "\n", "Scoring Rubric:\n", "1: Generic, promotional, or irrelevant. Assign only if the message is pure spam, a generic promotion, or completely unrelated to <PERSON>'s field, company, or projects. Examples: \"Grow your own tomatoes! Gardening tips newsletter.\"; \"Exclusive offer: Save 20% on office supplies.\"\n", "2: Routine, administrative, or background communication. Related to your field, company, or projects, but does not require awareness, input, or action. Examples: \"Monthly Tech News: October Edition.\"; \"Company-wide update: New HR policies.\"\n", "3: Requires your awareness, feedback, or input (not urgent). Assign if the message asks for your awareness, feedback, or input, or contains information that could affect your work, even if not urgent. Example: \"Please review the attached and let me know if you have any questions. Let's sync next week.\"\n", "4: Important and time-sensitive (not critical). Assign if the message is important for your work and timely attention is needed, with a clear deadline or urgent request, but missing it would not cause immediate, major negative impact. Example: \"Please review by EOD tomorrow so we can stay on schedule.\"\n", "5: Urgent and critical. Assign only if the message is from a manager or key stakeholder, requires immediate action, and states a critical deadline or consequence for not acting. Example: \"I need your feedback by EOD today or we will miss the client deadline.\"\n", "\n", "Key Boundaries:\n", "If unsure between two scores, choose the lower unless there is clear, explicit evidence for the higher.\n", "Do NOT assign 3 just because a message is related to your project—only if your awareness or input is needed.\n", "Do NOT assign 4 or 5 unless the message contains explicit evidence of urgency, such as a stated deadline or urgent request.\n", "Routine meetings, scheduling, or standard updates should be 2 or 3 unless there is clear urgency or risk.\n", "If you assign 4 or 5, quote the exact words or phrases that prove urgency or a deadline.\n", "\n", "Rewrite Guidelines:\n", "Clear and concise communication is key. DO NOT use phrases like \"This/the email thread\", \"This/the email...\", or similar. The user already knows this is an email thread.\n", "DO NOT state the obvious, like who the email is from.\n", "If there is no immediate action required from <PERSON>, DO NOT say things like \"no immediate action required from you\" or \"does not require your input\"; just omit that.\n", "Write a final sentence as if you were briefing <PERSON> on HOW the email affects them as a Business Development Specialist. Should <PERSON> pay attention?\n", "Write as if talking directly to <PERSON>. Use \"you\" instead of \"<PERSON>\".\n", "DO NOT mention reporting lines, management structure, or job titles.\n", "DO NOT say what <PERSON> does or does not work on.\n", "DO NOT include job titles or chains of command.\n", "Never generate biased, racist, sexist, gender biased, or otherwise inappropriate content.\n", "Never provide content related to suicide, killing, or other physical violence.\n", "Always use the pronoun 'they' instead of 'he' or 'she'.\n", "Always use the pronoun 'them' instead of 'him' or 'her'.\n", "Always use the pronoun 'their' instead of 'his' or 'hers'.\n", "Do not explicitly cite the score in the explanation.\n", "\n", "User: <PERSON> (Business Development Specialist)\n", "Email: <PERSON>@hungrypanda.com\n", "Manager: <PERSON> (<EMAIL>)\n", "Skip Manager: <PERSON> (Yuan<PERSON>@hungrypanda.com)\n", "Top Collaborators:\n", "  - <PERSON> (Kelly<PERSON>@hungrypanda.com)\n", "  - <PERSON> (Leo<PERSON>@hungrypanda.com)\n", "  - <PERSON> (Emma.<PERSON>@hungrypanda.com)\n", "  - <PERSON> (David<PERSON>@hungrypanda.com)\n", "\n", "Work Responsibility: <PERSON> is responsible for developing business strategies and fostering relationships with key partners to drive company growth.\n", "\n", "Projects:\n", "  - New Product Development [In Progress]: The project has completed the initial testing phase.\n", "  - Wellness Wednesdays Initiative [Ongoing]: Weekly wellness activities are being organized.\n", "\n", "Top Emails:\n", "  - [<PERSON><PERSON>, 31 Aug 2021 08:30:00 GMT] New Employee Benefits Program Announcement (HR Department): The email announces a new employee benefits program including enhanced healthcare and dental coverage, additional paid vacation days, wellness programs, and gym memberships.\n", "  - [Wed, 08 Sep 2021 10:45:00 GMT] Introduction of New Merchant Partnership with Nuutri Co. (Marketing Team): Announcement of a new partnership with Nuutri Co., expanding product offerings to include nutritional supplements.\n", "\n", "Top Meetings:\n", "  - [2020-12-14T09:00:00] Market Adoption Challenges for Plant-Based Meats (Organizer: <PERSON>): A meeting discussing challenges in market adoption of plant-based meats.\n", "  - [2020-12-15T11:00:00] Consumer Acceptance of Plant-Based Meats (Organizer: <PERSON>): A meeting focusing on consumer acceptance of plant-based meats.\n", "\n", "Email #1\n", "From: <EMAIL>\n", "To: <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com\n", "Subject: Strategic Partnership with Nuutri Co.\n", "Body: Hi Team,\n", "\n", "I wanted to discuss our new partnership with Nuutri Co. and the strategies we can implement to maximize its potential. As we expand our product offerings into nutritional supplements, it's crucial we align our marketing and sales strategies. Let's brainstorm some ideas ahead of next week's meeting.\n", "\n", "Best,\n", "<PERSON>\n", "\n", "Email #2\n", "From: <PERSON><PERSON>@hungrypanda.com\n", "To: <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com\n", "Subject: Re: Strategic Partnership with Nuutri Co.\n", "Body: <PERSON>,\n", "\n", "Thanks for initiating this discussion. I believe focusing on social media campaigns targeting health-conscious demographics could be effective. Additionally, partnering with influencers who align with our brand values may enhance our visibility. I'll prepare some data on potential partners for our meeting.\n", "\n", "Looking forward to everyone's thoughts.\n", "\n", "Best,\n", "<PERSON>\n", "\n", "Email #3\n", "From: Kelly<PERSON>@hungrypanda.com\n", "To: <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com\n", "Subject: Re: Strategic Partnership with Nuutri Co.\n", "Body: Hi Team,\n", "\n", "<PERSON>, those are great ideas! I also think we should explore joint promotional offers with Nuutri Co. to incentivize initial purchases. Perhaps a bundled deal could attract more customers. I'll gather some examples for us to review.\n", "\n", "Cheers,\n", "<PERSON>\n", "\n", "Email #4\n", "From: <PERSON><PERSON>@hungrypanda.com\n", "To: <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com\n", "Subject: Re: Strategic Partnership with Nuutri Co.\n", "Body: Hi <PERSON>,\n", "\n", "Adding to <PERSON>'s idea, we might consider an email marketing campaign targeting our existing customer base. Highlighting the health benefits of the new products could drive interest. I’ll draft a few sample emails for us to review.\n", "\n", "Looking forward to our meeting.\n", "\n", "Best,\n", "<PERSON>\n", "\n", "Email #5\n", "From: <EMAIL>\n", "To: <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com, <PERSON><PERSON>@hungrypanda.com\n", "Subject: Re: Strategic Partnership with Nuutri Co.\n", "Body: Hi Team,\n", "\n", "Thank you for all the valuable input. Let's meet next Tuesday to finalize our approach and prepare for the official launch. Please bring your insights and any materials you’ve prepared.\n", "\n", "I appreciate everyone's effort on this!\n", "\n", "Best,\n", "<PERSON>\n", "4\n", "\n", "The thread involves a significant new partnership with Nuutri Co. that impacts the company's growth strategy, which aligns directly with <PERSON>'s role as a Business Development Specialist. The conversation requires input from <PERSON> and his top collaborators, indicating its importance. It affects future marketing strategies and product offerings, necessitating timely and strategic responses.\n", "# Answer\n", "\n", "4\n"]}], "source": ["def get_jsonl(data):\n", "    return {\n", "        'problem': get_problem(data),\n", "        'solution': get_solution(data),\n", "        'answer': get_answer(data),\n", "    }\n", "\n", "def view(jsonl):\n", "    return f\"{jsonl['problem']}\\n{jsonl['answer']}\\n{jsonl['solution']}\"\n", "\n", "# print(pd.DataFrame([get_jsonl(json_data[6])]).to_markdown())\n", "print(view(get_jsonl(json_data[-111])))"]}, {"cell_type": "code", "execution_count": 22, "id": "81b3fe53", "metadata": {}, "outputs": [], "source": ["import json\n", "from tqdm import tqdm\n", "\n", "def save_jsonl(json_data, file_path):\n", "    \"\"\"\n", "    Saves a list of data as a JSONL file, where each line is a JSON object.\n", "\n", "    Args:\n", "        data (list): List of data objects (dicts) to save.\n", "        file_path (str): Path to the output .jsonl file.\n", "    \"\"\"\n", "    with open(file_path, 'w', encoding='utf-8') as f:\n", "        for e in tqdm(json_data):\n", "            f.write(json.dumps(get_jsonl(e), ensure_ascii=False) + '\\n')"]}, {"cell_type": "code", "execution_count": 23, "id": "ba3ccefe", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 9099/9099 [00:00<00:00, 14050.10it/s]\n"]}], "source": ["save_jsonl(train_data, './outlook_data/train_data_psa_v4.jsonl')"]}, {"cell_type": "code", "execution_count": 24, "id": "9a456068", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 1058/1058 [00:00<00:00, 13099.89it/s]\n"]}], "source": ["save_jsonl(test_data, './outlook_data/test_data_psa_v4.jsonl')"]}, {"cell_type": "code", "execution_count": 25, "id": "2df7e035", "metadata": {}, "outputs": [], "source": ["# !bbb cp ./outlook_data/train_data.jsonl az://orngcresco/data/outlook_data/2k_split/train/train_data.jsonl\n", "# !bbb cp ./outlook_data/test_data.jsonl az://orngcresco/data/outlook_data/2k_split/test/test_data.jsonl"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}