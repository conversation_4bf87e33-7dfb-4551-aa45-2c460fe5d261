"""
Utility functions and classes which are copied from the researcher-emulator repo and should be kept in sync.
"""

from datetime import datetime, timed<PERSON>ta
from enum import Enum
from typing import Callable, Optional


class EntityType(Enum):
    FILE = "file"
    WEBPAGE = "webpage"
    MESSAGE = "message"
    EMAIL = "email"
    PERSON = "person"
    MEETING = "meeting"
    MEETING_TRANSCRIPT = "meetingtranscript"

def get_entity_timestamp(entity: dict) -> Optional[str]:
    entity_type = entity['type'].lower()
    if entity_type == EntityType.FILE.value:
        return entity.get('lastModifiedTime')
    elif entity_type == EntityType.WEBPAGE.value:
        return None
    elif entity_type == EntityType.MESSAGE.value:
        return entity.get('timestamp')
    elif entity_type == EntityType.EMAIL.value:
        return entity.get('dateTimeSent')
    elif entity_type == EntityType.PERSON.value:
        return None
    elif entity_type == EntityType.MEETING.value:
        return entity.get('start')
    elif entity_type == EntityType.MEETING_TRANSCRIPT.value:
        return entity.get('start')
    return None

def create_dataset_filter(source: Optional[str] = None, recency_days: Optional[int] = None) -> Callable[[dict], bool]:
    def filter(entity: dict) -> bool:
        """
        Default filter that accepts all entities.
        """
        if source and entity['type'].lower() != source.lower():
            return False
        timestamp = get_entity_timestamp(entity)
        if recency_days is not None and timestamp:
            # Convert timestamp to datetime object
            entity_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            # Check if the entity is within the recency_days range
            return entity_time >= datetime.now() - timedelta(days=recency_days)
        return True
    return filter
