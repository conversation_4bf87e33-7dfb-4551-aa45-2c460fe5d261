from collections.abc import Collection
from enum import Enum
import functools
import os
from typing import Any, AsyncItera<PERSON>, Optional
from datetime import datetime, timedelta
import aiohttp
import blobfile
import chz
import structlog
import chat
from functions import FunctionNamespace
from functools import lru_cache
import tempfile
from sentence_transformers import SentenceTransformer
from lean_browser import base
from lean_browser.browser_use_container import BrowserUseState
from lean_browser.lean_browser_state import IncrementalLeanBrowserState, UniqueLeanBrowserState
from qstar.common import datapoint
from qstar.common.tools.berry_tool_interface import BerryTool, ToolConfig
from qstar.common.tools.browser.browser_tool import <PERSON><PERSON><PERSON>erTool, BrowserToolConfig
from researcher_msft.common.tools.enterprise.enterprise_dataset import (
    EnterpriseDataset,
    entity_to_text,
    get_entity_title,
    get_entity_url,
)
from researcher_msft.common.tools.enterprise.utils import (
    EntityType,
    get_entity_timestamp,
    create_dataset_filter,
)
from tool_use.browsing import page_contents as pc_lib
from tool_use.browsing.labrador_browse_utils import ScoredPageContents
from function_calling_tool import FunctionCallingTool, function_the_model_can_call

logger = structlog.stdlib.get_logger(component="EnterpriseBrowserTool")

@chz.chz(typecheck=True)
class EnterpriseBackend(base.Backend):
    source: str = "user_enterprise_data"
    entity_filter: Optional[str] = None
    """
    A backend that uses the EnterpriseDataset to search over static entity data.
    """
    enterprise_dataset: Optional[EnterpriseDataset] = chz.field(
        default=None, doc="The EnterpriseDataset to use for searching and fetching entities."
    )

    @chz.init_property
    def available_sources(self) -> list[str]:
        return [self.source, *[e.value for e in EntityType]] 

    @chz.init_property
    def default_source(self) -> str:
        return self.source

    def get_by_source(self, source: str | None) -> base.Backend:
        if (source is None) or (source == self.source):
            return self
        
        return EnterpriseBackend(source=self.source, entity_filter=source, enterprise_dataset=self.enterprise_dataset)

    async def search(
        self, query: str, topn: int, recency_days: int | None, session: aiohttp.ClientSession
    ) -> list[ScoredPageContents]:
        # Copy paste from EnterpriseBrowser._search_impl in researcher-emulator repo
        assert (
            self.enterprise_dataset is not None
        ), "EnterpriseBackend requires a dataset to be set."
        results = []
        source = self.entity_filter
        filter = create_dataset_filter(source=source, recency_days=recency_days)
        entities = self.enterprise_dataset.search(query, top_k=topn or 10, filter=filter)

        for entity in entities:
            url = get_entity_url(entity)
            title = get_entity_title(entity)

            # Create a search result
            results.append(
                ScoredPageContents(
                    url=url,
                    title=title,
                    text=entity_to_text(entity),
                    name2idx={},
                    urls={},
                    score=entity.get("score", 0.0),
                )
            )

        return results

    async def fetch(self, url: str, session: aiohttp.ClientSession) -> pc_lib.PageContents:
        assert (
            self.enterprise_dataset is not None
        ), "EnterpriseBackend requires a dataset to be set."
        entity = self.enterprise_dataset.get_entity_by_url(url)
        url = get_entity_url(entity)
        title = get_entity_title(entity)

        return pc_lib.PageContents(
            url=url,
            title=title,
            text=entity_to_text(entity),
            urls={},
            name2idx={},
        )


class EnterpriseBrowserTool(BerryBrowserTool):
    """
    A browser tool that uses the EnterpriseBackend to search and fetch entities.
    This tool is designed to work with the EnterpriseDataset.
    """

    def __init__(
        self,
        backend: EnterpriseBackend,
        encoding_name: str = "orion_200k_mmgen",
        max_search_results: int = 20,
        view_tokens: int = 1024,
        show_full_url_in_header: bool = False,
        tool_state: dict[str, Any] | None = None,
        deduplicate_images: bool = True,
        fovea: int | None = None,
        name: str = "enterprise",
        unique_tether_id: bool = False,
        browser_use_state: BrowserUseState | None = None,
        # Set if you don't want to yield messages w/ base64 in them, due to persisting in DB, etc.
        # TODO: Integrate deeper into lean browser stack / backends
        require_image_asset_pointers: bool = False,
        # Used to prevent us from blocking thread loop on n^2 processes for huge pages
        max_page_size: int | None = None,
        enable_wrap_lines_process_pool: bool = True,
        should_domain_limit: bool = False,
        third_party_connector_ids: list[str] | None = None,
        should_log_telemetry: bool = False,
    ):
        self.backend = backend
        self.require_image_asset_pointers = require_image_asset_pointers
        self.enable_wrap_lines_process_pool = enable_wrap_lines_process_pool
        self.should_domain_limit = should_domain_limit
        state_cls = UniqueLeanBrowserState if unique_tether_id else IncrementalLeanBrowserState
        if tool_state is None:
            self.tool_state = state_cls()
        else:
            self.tool_state = state_cls.model_validate(tool_state)

        self.encoding_name = encoding_name
        self.max_search_results = max_search_results
        self.view_tokens = view_tokens
        self.show_full_url_in_header = show_full_url_in_header
        self.deduplicate_images = deduplicate_images
        self.fovea = fovea
        self.browser_use_state = browser_use_state
        self.max_page_size = max_page_size
        self.should_log_telemetry = False

        self.third_party_connector_ids = third_party_connector_ids
        self.should_log_telemetry = should_log_telemetry

    @function_the_model_can_call
    def search_chat(
        self,
        query: str,
    ) -> AsyncIterator[chat.Message]:
        """
        Search the user's enterprise Teams, channel and meeting messages and returns matching messages.

        Args:
            query (str): A well-formatted query
                - filter results by sender or receiver. e.g. "messages sent by <FULL NAME>" or "messages to <FULL NAME>"
                - filter results by who is mentioned. e.g. "messages that mention me" or "messages that mention <FULL NAME>"
                - terms like "recent/latest/yesterday" or specific dates can be used to filter the results. e.g. "recent messages from <FULL NAME>", "messages about <TOPIC> on <DATE>", or "messages about <TOPIC> yesterday"
                - filter by the state of the email:
                - "important": for chats that are explicitly tagged as important (and therefore urgent) by the sender
                - "unread": for messages that have not been read by the user.
                - "read": for messages that have been read by the user.
                - "flagged": for messages that have been flagged by the user for a follow-up action
                - filter results by attachment. e.g. "recent messages with a PDF attachment" or "latest messages from <FULL NAME> with an attachment"
                - filter results that contain tasks, action items, to-dos, etc for a specific person. "recent messages with tasks for <FULL NAME>"
                - filter results by teams channel or chat name. e.g. "recent messages sent to the <NAME> channel" or "messages about <TOPIC> sent to <NAME> chat yesterday"

                Examples:
                    Given the context (timestamp = "Wed, 05 Jul 2023 08:32:15 GMT-05:00", location = "The user is located in CHICAGO.", user_profile = {'Name': 'Samantha Brown', 'Job Title': 'MARKETING DIRECTOR', 'Manager': 'Rachel Turner', 'Skip Manager': 'James Cooper'}), here are some examples of how to use this tool.
                        - show me chats where Dakota mentioned the holiday party
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"Dakota\"}" }`  # First find out which Dakota we're probably referring to
                            - `"function": { "name": "enterprise.search_chat", "arguments": "{\"query\": \"messages where Dakota <LAST NAME> mentioned holiday party\"}" }`  # Get the relevant messages
                        - catch me up on Rachel's messages
                            - `"function": { "name": "enterprise.search_chat", "arguments": "{\"query\": \"recent messages from Rachel Turner\"}" }`
                            - `"function": { "name": "enterprise.search_emails", "arguments": "{\"query\": \"recent messages from Rachel Turner\"}" }`
                        - my unread chats
                            - `"function": { "name": "enterprise.search_chat", "arguments": "{\"query\": \"recent unread messages\"}" }`
                        - List the messages where I was mentioned in the objectives
                            - `"function": { "name": "enterprise.search_chat", "arguments": "{\"query\": \"messages where I was mentioned in objectives\"}" }`
        """
        return self.search(query=query, source='message')
    
    @function_the_model_can_call
    def search_emails(
        self,
        query: str,
    ) -> AsyncIterator[chat.Message]:
        """
        Search the user's enterprise email messages and returns matching results.

        Args:
            query (str): a well-formatted query string
                - filter results by sender or receiver. e.g. "emails sent by <FULL NAME>" or "emails to <FULL NAME>"
                - filter results by who is mentioned. e.g. "emails that mention me" or "emails that mention <FULL NAME>"
                - terms like "recent/latest/yesterday" or specific dates can be used to filter the results.  e.g. "recent emails from <FULL NAME>", "emails about <TOPIC> on <DATE>", or "emails about <TOPIC> yesterday"
                - filter by the state of the email:
                    - "important": for emails that are likely to be important to the user
                    - "unread": for emails that have not been read by the user.
                    - "read": for emails that have been read by the user.
                    - "flagged": for emails that have been flagged by the user for a follow-up action
                - filter results by attachment. e.g. "recent emails with a PDF attachement" or "latest email from <FULL NAME> with an attachment"
                - filter results that contain tasks, action items, to-dos, etc for a specific person. "recent emails with tasks for <FULL NAME>"

                Examples:
                    Given the context (timestamp = "Fri, 24 Mar 2023 15:21:07 GMT-07:00", location = "The user is located in DETROIT.", user_profile = {'Name': 'Chris Ross','Job Title':'PROGRAM MANAGER','Manager':'Zhisheng Wei','Skip Manager':'Ryan Boyd'}), here are some examples of how to use this tool.
                        - "messages in my inbox while out of office"
                            - `"function": { "name": "enterprise.search_emails", "arguments": "{\"query\": \"recent unread emails\"}" }`
                        - emails from last week
                            - `"function": { "name": "enterprise.search_emails", "arguments": "{\"query\": \"emails from last week\"}" }`
                        - What did Zhisheng mention in the email that included the Meeting_Agenda_Feb_17.docx attachment
                            - `"function": { "name": "enterprise.search_emails", "arguments": "{\"query\": \"recent email from Zhisheng Wei with Meeting_Agenda_Feb_17.docx attached\"}" }`
                        - Who was the last person I exchanged emails with
                            - `"function": { "name": "enterprise.search_emails", "arguments": "{\"query\": \"recent emails from Chris Ross\"}" }`
                        - Find the Emails where I was asked for a review
                            - `"function": { "name": "enterprise.search_emails", "arguments": "{\"query\": \"emails where I was asked for a review\"}" }`
        """
        return self.search(query=query, source='email')
    
    @function_the_model_can_call
    def search_files(
        self,
        query: str,
        topn: Optional[int] = None,
        recency_days: Optional[int] = None,
    ) -> AsyncIterator[chat.Message]:
        """
        Searches Sharepoint, Onedrive, and various Microsoft Graph Connectors and returns matching files, documents, wiki pages, technical and learning resources, operational and organizational support, websites, links, and etc. that the user has access to.

        Args:
            query (str): a well-formatted query
                - get specific files by searching for their exact file name. 
                - use full URLs if available 
                - filter results by people who have contributed to the files e.g. "files about <TOPIC> by <FULL NAME>"
                - filter results by who they are shared with. e.g. "files shared with <FULL NAME>"
                - filter results by people who are mentioned or tagged in the file. e.g. "files that mention <FULL NAME>"
                - terms like "recent/latest/yesterday" or specific dates can be used to filter the results.  e.g. "recent files from <FULL NAME>", "files about <TOPIC> from <DATE>", or "file about <TOPIC> shared with <FULL NAME> yesterday"
                - filter results by the type of file.  Valid values include file extensions like "docx", "pptx", "xlsx", "pdf", "txt", etc and common ways of referring to file extensions like "deck" (for ppt), "presentation" (for ppt), "video" (for mp4), or "spreadsheet" (for xlsx) are also valid filetype values. Note that "document", "report", or "summary" are **NOT** valid filetype values as they do not map to a specific file extension. "ppt files about <TOPIC>" or "slide decks about <TOPIC>"
                - the terms "shared", "authored", "modified", "opened", "attached", and "commented" will filter results by specific interactions.  E.g. "files authored by <FULL NAME>", "recent files opened by <FULL NAME>"
                - filter results to Sharepoint or OneDrive. e.g. "recent files about <TOPIC> in OneDrive"
                
                Examples:
                    Given the context (timestamp = "Thu, 20 Jun 2019 09:25:47 GMT-03:00", location = "The user is located in MIAMI.", user_profile = {'Name': 'Olivia Green', 'Job Title': 'EVENT COORDINATOR', 'Manager': 'Daniel White', 'Skip Manager': 'Patricia Lewis'}), here are some examples of how to use this tool.
                        - Get me pdf files with the explanations for the geometry assignment from Daniel
                            - `"function": { "name": "enterprise.search_files", "arguments": "{\"query\": \"pdf files with the explanations for the geometry assignment from Daniel\"}" }`
                        - Summarize https://microsoft-my.sharepoint-df.com/personal/ogreen/Documents/ProdSpecs.xlsx
                            - `"function": { "name": "enterprise.search_files", "arguments": "{\"query\": \"https://microsoft-my.sharepoint-df.com/personal/ogreen/Documents/ProdSpecs.xlsx\"}" }`
                        - files my manager shared with me recently
                            - `"function": { "name": "enterprise.search_files", "arguments": "{\"query\": \"files Daniel White shared with me recently\"}" }`
                        - What files has Louisa recently modified?
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"Louisa\"}" }`  # First search for which Louisa
                            - `"function": { "name": "enterprise.search_files", "arguments": "{\"query\": \"files that Louisa <LAST NAME> recently modified\"}" }`  # Then use the full name to search for relevant files
                    topn (Optional[int], optional): Maximum number of results to return
                    recency_days (Optional[int], optional): Limit results to items from the last N days
        """
        return self.search(query=query, topn=topn or 10, recency_days=recency_days, source='file')
    
    @function_the_model_can_call
    def search_meetings(
        self,
        query: str,
    ) -> AsyncIterator[chat.Message]:
        """
        Search the user's work calendar and returns matching events.  This tool returns basic details about a meeting (name, time, attendees, etc.) along with related messages, emails, documents, transcript snippets, action items, etc. For requests to prepare for or recap a meeting call **only** this tool and wait for results before deciding to search other domains.

        Args:
            query (str): a well-formatted query string
                - filter results by attendees my specifying their names (e.g. "upcoming meetings with <FULL NAME>")
                - filter results by the topic of the meeting (e.g. "past meetings about <TOPIC>")
                - terms like "past/future/recent/next/latest/yesterday/tomorrow" etc. will filter/sort results to the relevant time period (e.g. "my next meeting with <FULL NAME>", "recent meetings about <TOPIC>", "meetings next week with <FULL NAME> in the future", "meeting about <TOPIC> on past Tuesday"). 
                - a keyword of "past" or "future" ***must always*** be used in the query. If the user uses language such as "did", "were" or "was", this indicates "past". If the user uses language such as "do", "is", "are", or "will", this indicates "future". If the user mentions specific time frames such as "Monday", the terms "past", "future", "upcoming", etc. should be used in conjunction with specific time frames
                - also filter on specific dates and times (e.g. "meetings on Monday after 4PM")
                - filter by how the user has responded to in the invite
                    - "notresponded": For events the user has not explicitly responded to.
                    - "accepted": For events the user explicitly marked as "accepted".
                    - "followed": For events the user explicitly marked as "follow" (which means they won't attend but will follow-up/get a recap)
                    - "tentative": For events the user explicitly marked as "tentative".
                    - "declined": For events the user explicitly marked as "declined".
                    - "canceled": For events that have been canceled.
                Examples:
                    Given the context (timestamp = "Fri, 15 Apr 2022 13:27:18 GMT-05:00", location = "The user is located in INDIANAPOLIS.", user_profile = {'Name': 'Laura Peterson', 'Job Title': 'PUBLIC RELATIONS SPECIALIST', 'Manager': 'Tom Richards', 'Skip Manager': 'Nancy Wright'}), here are some examples of how to use this tool.
                        - action items from Laura/Sally 1:1
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"last Laura/Sally 1:1\", \"query_type\":\"recap\"}" }`
                        - meeting topics still need to be addressed or resolved
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"recent meetings\", \"query_type\":\"calendar\"}" }`  # First get the relevant meetings
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"<meeting name> at <datetime>\", \"query_type\":\"prep_and_recap\"}" }`  # For each relevant meeting returned in the previous search to get related "prep and recap" materials
                        - what meetings do I have with Nancy
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"upcoming meetings with Nancy Wright\", \"query_type\":\"calendar\"}" }`
                        - what meetings did I have with Tom
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"past meetings with Tom\", \"query_type\":\"calendar\"}" }`
                        - tasks assigned from shiproom
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"last shiproom meeting\", \"query_type\":\"recap\"}" }`
                        - next meeting with Tom
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"next meeting with Tom\", \"query_type\":\"calendar\"}" }`
                        - What did Mishra say during the client strategy meeting yesterday at 2pm?
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"client strategy meeting yesterday at 2pm\", \"query_type\":\"recap\"}" }`
                        - Help me prepare for my meetings after 11am tomorrow
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"meetings tomorrow after 11am\", \"query_type\":\"calendar\"}" }`  # First get the relevant meetings
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"<meeting name> at <datetime>\"}\", \"query_type\":\"prep\" }`  # For each relevant meeting returned in the previous search to get related "prep" materials
                        - Key findings and actions from the scheduling process
                            - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"past scheduilng process\", \"query_type\":\"recap\"}" }`
                        - Prep materials for the Contoso order review next Monday
                        - `"function": { "name": "enterprise.search_meetings", "arguments": "{\"query\": \"Contoso order review next Monday\", \"query_type\":\"prep\"}" }`
        """
        return self.search(query=query, source='meeting')
    
    @function_the_model_can_call
    def search_transcripts(
        self,
        query: str,
    ) -> AsyncIterator[chat.Message]:
        """
        Search within the user's meeting transcripts and returns matching transcript snippets.

        Args:
            query (str): a well-formatted query string
                - get transcripts by searching through their content
                - filter results by the topic of the transcript (e.g. "what was said about <TOPIC>")
                - terms like "recent/latest/yesterday" or specific dates can be used to filter the results. e.g. "recent updates about <TOPIC>", "updates about <TOPIC> from <DATE>", or "updates about <TOPIC> yesterday"
                
                Examples:
                    Given the context (timestamp = "Thu, 20 Jun 2019 09:25:47 GMT-03:00", location = "The user is located in TEXAS.", user_profile = {'Name': 'Rachel Green', 'Job Title': 'EVENT COORDINATOR', 'Manager': 'Daniel White', 'Skip Manager': 'Patricia Lewis'}), here are some examples of how to use this tool.
                        - what is the latest about viva engage
                            - `"function": { "name": "enterprise.search_transcripts", "arguments": "{\"query\": \"latest about viva engage\"}" }`
                        - what was said about feature store last week
                            - `"function": { "name": "enterprise.search_transcripts", "arguments": "{\"query\": \"feature store last week\"}" }`
                        - who works on the marketing campaign? who can I talk to learn more about this?
                            - `"function": { "name": "enterprise.search_transcripts", "arguments": "{\"query\": \"marketing campaign\"}" }`
        """
        return self.search(query=query, source='meetingtranscript')
    
    @function_the_model_can_call
    def search_people(
        self,
        query: str,
    ) -> AsyncIterator[chat.Message]:
        """
        Search for an individual contact's **static** information such as direct reports, expertise, office location, contact information, or related activities within the enterprise.

        Args:
            query (str): short phrase that includes a name or topic
                - "<FIRST NAME>" gets a list of people with a first name that sounds like the one provided sorted by the relevance to the user. This is very useful for finding out which person the user is referring to.
                - "<FULL NAME>" details about <FULL NAME> including office location
                - "<FULL NAME> direct reports" gets the people who report to <FULL NAME>
                - "<TOPIC>" gets a list of people with <TOPIC> listed in their profile
                - "<FULL NAME> <TOPIC>" gets people who have interacted with <FULL NAME> (e.g. had meetings, sent emails, etc) about <TOPIC>
                - "people in <DEPARTMENT>" gets a list of people who are in <DEPARTMENT>
                
                Note: when searching for people by name, you must provide a FULL NAME. If you only have a FIRST NAME, you must first learn FULL NAME before doing any other searches.
                
                Examples:
                    Given the context (timestamp = "Mon, 19 Jul 2021 14:12:09 GMT-06:00", location = "The user is located in MEXICO CITY.", user_profile = {'Name': 'Carlos Martinez', 'Job Title': 'HUMAN RESOURCES MANAGER', 'Manager': 'Lucia Gutierrez', 'Skip Manager': 'Jorge Ramirez'}), here are some examples of how to use this tool.
                        - who is on John's team
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"John\"}" }`  # First get John's full name
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"John <LAST NAME> direct reports\"}" }`  # Use the full name to get direct reports
                        - find people who know about machine learning
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"machine learning\"}" }`
                        - find people in the marketing department
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"people in the marketing department\"}" }`
                        - find contact info for Bob from Goldman Sachs
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"Bob from Goldman Sachs\"}" }`
                        - find people who report to the user
                            - `"function": { "name": "enterprise.search_people", "arguments": "{\"query\": \"Carlos Martinez direct reports\"}" }`
        """
        return self.search(query=query, source='person')

    @classmethod
    def get_tool_name(cls) -> str:
        return "enterprise"

    @classmethod
    @functools.cache
    def get_names_of_functions_the_model_can_call(cls) -> Collection[str]:
        return [
            # Browser methods
            "open",
            "find",
            # "search", # Disable search since Researcher does not expose it
            "search_chat",
            "search_emails",
            "search_files",
            "search_meetings",
            "search_transcripts",
            "search_people",
        ]


@chz.chz
class SentenceTransformerConfig:
    model_path: str = chz.field(
        doc="Path to the sentence transformer model",
    )
    model_definition_path: str = chz.field(
        default="snapshots/c9745ed1d9f207416be6d2e6f8de32d1f16199bf/",
        doc="Path to the model definition within the downloaded model assets",
    )

    local_model_download_path: str = "/tmp/sentence_transformer_model"

    def create_transformer(self) -> Any:
        logger.info(
            "Downloading sentence transformer model",
            model_path=self.model_path,
            local_path=self.local_model_download_path,
        )
        if not os.path.exists(self.local_model_download_path):
            copy_blob(self.model_path, self.local_model_download_path)

        return SentenceTransformer(
            os.path.join(self.local_model_download_path, self.model_definition_path), device="cpu"
        )
    
def copy_blob(src_dir, dest_dir):
    if not blobfile.isdir(src_dir):
        blobfile.copy(src_dir, dest_dir)
        return
    if not blobfile.exists(dest_dir):
        blobfile.makedirs(dest_dir)
    for file_path in blobfile.listdir(src_dir):
        src_path = blobfile.join(src_dir, file_path)
        dest_path = blobfile.join(dest_dir, file_path)
        copy_blob(src_path, dest_path)

@lru_cache(maxsize=1)
def initialize_dataset_from_path(
    entity_file_uri: str,
    sentence_transformer: SentenceTransformerConfig
) -> EnterpriseDataset:
    transformer = sentence_transformer.create_transformer()
    entity_file_download_path = tempfile.mktemp("enterprise_entity_files")
    logger.info(
        "Downloading entity files from URI",
        entity_file_uri=entity_file_uri,
        download_path=entity_file_download_path,
    )
    copy_blob(entity_file_uri, entity_file_download_path)
    return EnterpriseDataset.from_entity_files(
        entity_file_download_path, transformer=transformer
    )

@chz.chz
class EnterpriseBrowserToolConfig(ToolConfig):
    backend: EnterpriseBackend = chz.field(default_factory=EnterpriseBackend)
    tool_timeout: int = 60 * 60

    sentence_transformer: SentenceTransformerConfig = chz.field(
        default_factory=SentenceTransformerConfig
    )
    entity_file_uri: Optional[str] = None

    def get_tool_name(self) -> str:
        return EnterpriseBrowserTool.get_tool_name()

    def instruction(self, datapoint: datapoint.HarmonyDatapoint | None = None) -> str:
        tool = EnterpriseBrowserTool(backend=EnterpriseBackend())
        return tool.instruction()

    def get_function_namespace(
        self, datapoint: datapoint.HarmonyDatapoint | None = None
    ) -> FunctionNamespace:
        return FunctionNamespace(
            name=self.get_tool_name(),
            description="Tools in the `enterprise` namespace can be used for gathering information from enterprise sources.",
            functions=EnterpriseBrowserTool.get_function_calling_function_schemas(),
        )

    def get_names_of_functions_the_model_can_call(
        self, datapoint: "datapoint.HarmonyDatapoint | None" = None
    ) -> Collection[str]:
        return EnterpriseBrowserTool.get_names_of_functions_the_model_can_call()

    def unique_descriptor_for_variants(self) -> str:
        backend_dataset_name = self.backend.enterprise_dataset.name if self.backend.enterprise_dataset else "no_dataset"

        return f"enterprise_browser_{backend_dataset_name}"


    def _initialize_dataset(
        self,
        datapoint: datapoint.HarmonyDatapoint | None = None) -> EnterpriseDataset:
        if self.entity_file_uri:
            dataset = initialize_dataset_from_path(
                entity_file_uri=self.entity_file_uri,
                sentence_transformer=self.sentence_transformer
            )
        else:
            assert (
                datapoint and datapoint.metadata
            ), "EnterpriseBrowserToolConfig requires a datapoint with metadata"
            metadata = datapoint.metadata
            context = metadata["context"]
            entities = context.get("ENTITIES", [])
            transformer = self.sentence_transformer.create_transformer()
            dataset = EnterpriseDataset(entities=entities, transformer=transformer)

        return dataset


    def _initialize_dataset(
        self,
        datapoint: datapoint.HarmonyDatapoint | None = None) -> EnterpriseDataset:
        if self.entity_file_uri:
            dataset = initialize_dataset_from_path(
                entity_file_uri=self.entity_file_uri,
                sentence_transformer=self.sentence_transformer
            )
        else:
            assert (
                datapoint and datapoint.metadata
            ), "EnterpriseBrowserToolConfig requires a datapoint with metadata"
            metadata = datapoint.metadata
            context = metadata["context"]
            entities = context.get("ENTITIES", [])
            transformer = self.sentence_transformer.create_transformer()
            dataset = EnterpriseDataset(entities=entities, transformer=transformer)

        return dataset

    def _initialize_tool(
        self,
        datapoint: datapoint.HarmonyDatapoint | None = None,
        **kwargs: Any,
    ) -> BerryTool:
        dataset = self._initialize_dataset(datapoint=datapoint)
        tool = EnterpriseBrowserTool(
            backend=EnterpriseBackend(enterprise_dataset=dataset),
        )
        return tool
