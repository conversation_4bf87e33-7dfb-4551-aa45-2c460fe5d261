from typing import Any, Dict, Optional, cast
import json
import os
import tempfile
import chz
import pytest
from boostedblob import cli as bbb
from chat import chat
from qstar.common.datapoint import HarmonyCompletionDatapoint
from qstar.common.tools.browser.browser_tool import Berry<PERSON>rowserTool
from qstar.curriculums.dataset_config import HarmonyCompletionDatasetConfig
from researcher_msft.common.tools.enterprise.enterprise_browser_tool import (
    EnterpriseBackend,
    EnterpriseBrowserTool,
    EnterpriseBrowserToolConfig,
)

@pytest.fixture
def test_entities() -> list[Dict[str, Any]]:
    return [
        {
            "id": "file1",
            "type": "file",
            "content": (
                "Project Lapras Report\n"
                "=====================\n\n"
                "Overview:\n"
                "Project Lapras aims to design and manufacture mechanical flippers for dogs, enabling improved mobility for canines with limb impairments. The project brings together engineers, veterinarians, and animal behaviorists to ensure the devices are both functional and comfortable for the animals.\n\n"
                "Progress Update:\n"
                "During the past week, the engineering team finalized the prototype design for the adjustable flipper mechanism. Initial 3D-printed models have been assembled and are currently undergoing fit and comfort testing with volunteer dogs. Early feedback from veterinarians has been positive, particularly regarding the lightweight materials and secure attachment system.\n\n"
                "Next Steps:\n"
                "The team will focus on refining the flipper's range of motion and durability based on test results. Additional user trials are scheduled for next month, and the documentation for assembly and fitting procedures is being drafted. Collaboration with animal rehabilitation specialists will continue to ensure the product meets the highest standards of safety and usability."
            ),
            "title": "Project Lapras Report",
        },
        {
            "id": "person1",
            "type": "person",
            "displayName": "John Doe",
            "profile": "Software Engineer",
            "myNotes": "Met at conference",
        },
        {
            "id": "email1",
            "type": "email",
            "body": (
                "Hello team,\n\n"
                "I wanted to provide an update on the progress of our project over the past week. "
                "The development team has successfully completed the initial implementation of the new authentication module, "
                "and preliminary testing indicates that it is functioning as expected. We have also integrated the feedback "
                "from last week's review session, which has helped us streamline the user onboarding process. The documentation "
                "for the API endpoints is now available in the shared drive, and I encourage everyone to review it and provide any suggestions.\n\n"
                "Looking ahead, our next steps include conducting a more comprehensive round of QA testing and preparing for the upcoming demo. "
                "Please ensure that any outstanding tasks assigned to your teams are addressed by the end of this week. If you encounter any blockers, "
                "do not hesitate to reach out so we can resolve them promptly. Thank you all for your continued hard work and dedication to making this project a success.\n"
            ),
            "subject": "Project Update",
        },
    ]

BRIX_CLUSTER_TO_STORAGE_MAP = {
    "prod-westus2-19": "orngwus2cresco",
    "prod-uksouth7": "orngcresco",
}

def get_storage_account() -> str:
    if brix_cluster := os.environ.get("BRIX_CLUSTER"):
        return BRIX_CLUSTER_TO_STORAGE_MAP[brix_cluster]
    # default to orngwus2cresco if not set
    return "orngwus2cresco"

def _make_datapoint(entities: list[Dict[str, Any]]) -> HarmonyCompletionDatapoint:
    """Makes a dummy datapoint for tool initialization."""
    ds_config = chz.Blueprint(HarmonyCompletionDatasetConfig).make_from_argv(
        [
            "dataset_id=dummy",
            "...grader=qstar.graders.mathgen_grader:MathgenGrader",
        ]
    )
    datapoint = HarmonyCompletionDatapoint(
        dataset_config=ds_config,
        problem="foo?",
        metadata={"context": {"ENTITIES": entities}},
    )
    return datapoint


def _make_browser_tool(entities: list[dict], use_entity_file: bool=False, args: Optional[list[str]]=None) -> BerryBrowserTool:
    storage_account = get_storage_account()
    args=[f'sentence_transformer.model_path=az://{storage_account}/data/joclausm/models--sentence-transformers--all-MiniLM-L6-v2'] if not args else args
    if use_entity_file:
        fp, entities_file = tempfile.mkstemp("enterprise_entities")
        with open(fp, "w") as f:
            json.dump(entities, f)
        args = [*args, f"entity_file_uri={entities_file}",]
        entities = []
    conf = chz.Blueprint(EnterpriseBrowserToolConfig).make_from_argv(list(args))
    tool = conf.initialize_tool(datapoint=_make_datapoint(entities))
    tool = cast(BerryBrowserTool, tool)
    return tool

async def call_tool_helper(
    tool: BerryBrowserTool,
    function_name: str,
    **functions_kwargs,
) -> str:
    tool_name = EnterpriseBrowserTool.get_tool_name()
    message = chat.Message.assistant(recipient=f"{tool_name}.{function_name}", content=json.dumps(functions_kwargs))
    results = []
    async for response in tool.process(message):
        results.append(response)
    assert len(results) == 1, f"Expected one result for the {function_name} command"
    assert results[0].recipient == "all", "Expected the recipient to be 'all'"
    assert isinstance(
        results[0].content, chat.TetherBrowsingDisplay
    ), "Expected content to be a TetherBrowsingDisplay"
    assert results[0].content.result is not None, "Expected content result to be not None"
    return results[0].content.result

@pytest.mark.asyncio
@pytest.mark.parametrize("use_entity_file", [True, False], ids=["use_entity_file", "no_entity_file"])
async def test_enterprise_backend_init(test_entities: list[Dict[str, Any]], use_entity_file: bool) -> None:
    tool = _make_browser_tool(test_entities, use_entity_file)
    assert isinstance(tool.backend, EnterpriseBackend)
    enterprise_dataset = tool.backend.enterprise_dataset
    assert enterprise_dataset is not None, "EnterpriseBackend requires a dataset to be set."

def test_enterprise_browser_tool_names() -> None:
    assert EnterpriseBrowserTool.get_tool_name() == "enterprise", "Expected the tool name to be 'enterprise'"
    assert "search_files" in EnterpriseBrowserTool.get_names_of_functions_the_model_can_call(), "Expected 'search_file' to be in the function names"
    assert "search" not in EnterpriseBrowserTool.get_names_of_functions_the_model_can_call(), "Expected 'search' to not be in the function names"
    assert "open" in EnterpriseBrowserTool.get_names_of_functions_the_model_can_call(), "Expected 'open' to be in the function names"

@pytest.mark.asyncio
async def test_enterprise_backend_search(test_entities: list[Dict[str, Any]]) -> None:
    tool = _make_browser_tool(test_entities)
    result = await call_tool_helper(tool, "search", query="review session from last week", topn=1)
    assert (
        result
        == "L0: # 【0†Project Update†email】\nL1: Hello team,\nL2: \nL3: I wante..."
    ), "Expected the content to match the email body"

    result = await call_tool_helper(tool, "open", cursor=-1, id=0)
    lines = result.split("\n")
    assert lines[0] == "L0: Hello team,", "Expected the first line to match the email greeting"
    assert lines[-1] == "L16: ", "Expected the last line to be empty"
    assert len(lines) == 17, "Expected the email body to have 17 lines"

@pytest.mark.asyncio
async def test_enterprise_backend_search_files(test_entities: list[Dict[str, Any]]) -> None:
    tool = _make_browser_tool(test_entities)
    result = await call_tool_helper(tool, "search_files", query="project update", topn=1)
    assert (
        result
        == "L0: # 【0†Project Lapras Report†file】\nL1: Project Lapras Repor..."
    ), "Expected the content to match the file body"

    result = await call_tool_helper(tool, "open", cursor=-1, id=0)
    lines = result.split("\n")
    assert lines[0] == "L0: Project Lapras Report", "Expected the first line to match the file"
    assert lines[-1] == "L21:  product meets the highest standards of safety and usability."
    assert len(lines) == 22, "Expected the file body to have 9 lines"
